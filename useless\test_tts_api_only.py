#!/usr/bin/env python3
"""只测试TTS API调用，不播放音频"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_tts_api_only():
    print("=== TTS API调用测试 ===\n")
    
    try:
        import requests
        import json

        # 直接读取配置文件
        with open("src/config/config.json", "r", encoding="utf-8") as f:
            config = json.load(f)

        openai_tts_config = config["openai_tts"]

        api_key = openai_tts_config["api_key"]["value"]
        base_url = openai_tts_config["base_url"]["value"]
        model = openai_tts_config["model"]["value"]
        voice = openai_tts_config["voice"]["value"]
        
        print(f"API Key: {api_key[:10]}...")
        print(f"Base URL: {base_url}")
        print(f"Model: {model}")
        print(f"Voice: {voice}")
        
        # 测试API调用
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        
        data = {
            "model": model,
            "input": "测试",
            "voice": voice
        }
        
        print("\n🌐 发送TTS API请求...")
        response = requests.post(
            f"{base_url}/audio/speech",
            headers=headers,
            json=data,
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            audio_data = response.content
            print(f"✅ API调用成功！音频数据长度: {len(audio_data)} 字节")
            
            # 保存音频文件用于验证
            with open("test_output.mp3", "wb") as f:
                f.write(audio_data)
            print("✅ 音频已保存到 test_output.mp3")
        else:
            print(f"❌ API调用失败: {response.text}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_tts_api_only()
