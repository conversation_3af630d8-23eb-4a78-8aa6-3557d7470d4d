#!/usr/bin/env python3
"""
测试微信窗口截图功能
"""

import os
import sys
import time
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.wechat_reconnect.reconnect_manager import WeChatReconnectManager

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_screenshot():
    """测试截图功能"""
    print("=== 测试微信窗口截图功能 ===")
    
    try:
        # 创建一个简单的配置对象
        class TestReconnectConfig:
            def __init__(self):
                self.enable_auto_reconnect = True
                self.check_interval = 60
                self.max_retry_attempts = 3
                self.qrcode_retry_interval = 120
                self.email_enabled = False
                self.smtp_server = ""
                self.smtp_port = 587
                self.sender_email = ""
                self.sender_password = ""
                self.recipient_email = ""

        class TestConfig:
            def __init__(self):
                self.wechat_reconnect = TestReconnectConfig()
        
        config = TestConfig()
        
        # 创建重连管理器（不需要真实的微信实例来测试截图）
        manager = WeChatReconnectManager(
            wx_instance=None,  # 测试时不需要真实实例
            config=config
        )
        
        # 测试截图功能
        print("正在尝试截图微信窗口...")
        screenshot_path = os.path.join("data", "cache", f"test_screenshot_{int(time.time())}.png")
        
        # 确保目录存在
        os.makedirs(os.path.dirname(screenshot_path), exist_ok=True)
        
        # 调用截图方法
        success = manager._capture_wechat_window(screenshot_path)
        
        if success and os.path.exists(screenshot_path):
            print(f"✅ 截图成功！文件保存到: {screenshot_path}")
            print(f"文件大小: {os.path.getsize(screenshot_path)} 字节")
            
            # 显示文件信息
            from PIL import Image
            try:
                img = Image.open(screenshot_path)
                print(f"图片尺寸: {img.size}")
                print(f"图片格式: {img.format}")
                print(f"图片模式: {img.mode}")
            except Exception as e:
                print(f"无法读取图片信息: {e}")
                
        else:
            print("❌ 截图失败")
            
    except ImportError as e:
        print(f"❌ 缺少必要的库: {e}")
        print("请安装: pip install pillow pywin32")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_qrcode_method():
    """测试获取二维码方法"""
    print("\n=== 测试获取微信登录界面方法 ===")
    
    try:
        # 创建一个简单的配置对象
        class TestReconnectConfig:
            def __init__(self):
                self.enable_auto_reconnect = True
                self.check_interval = 60
                self.max_retry_attempts = 3
                self.qrcode_retry_interval = 120
                self.email_enabled = False
                self.smtp_server = ""
                self.smtp_port = 587
                self.sender_email = ""
                self.sender_password = ""
                self.recipient_email = ""

        class TestConfig:
            def __init__(self):
                self.wechat_reconnect = TestReconnectConfig()
        
        config = TestConfig()
        
        # 创建重连管理器
        manager = WeChatReconnectManager(
            wx_instance=None,
            config=config
        )
        
        # 测试获取二维码方法
        print("正在尝试获取微信登录界面...")
        result_path = manager._get_qrcode()
        
        if result_path and os.path.exists(result_path):
            print(f"✅ 获取成功！文件保存到: {result_path}")
            print(f"文件大小: {os.path.getsize(result_path)} 字节")
        else:
            print("❌ 获取失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("微信窗口截图功能测试")
    print("请确保微信已经打开")
    input("按回车键开始测试...")
    
    test_screenshot()
    test_qrcode_method()
    
    print("\n测试完成！")
