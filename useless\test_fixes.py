#!/usr/bin/env python3
"""
测试修复后的多模态LLM和消息定时器功能
"""

import sys
import os
import time
import threading
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_smart_message_merge():
    """测试智能消息合并功能"""
    print("=" * 50)
    print("测试智能消息合并功能")
    print("=" * 50)

    # 直接实现智能消息合并逻辑，不依赖完整的类
    def _smart_merge_messages(messages: list) -> str:
        """智能合并消息，优化图片和文字的组合处理"""
        if not messages:
            return ""

        # 如果只有一条消息，直接返回
        if len(messages) == 1:
            return messages[0]

        # 分离文字消息和图片消息
        text_parts = []
        image_parts = []

        for msg in messages:
            if "[IMAGE:" in msg and "]" in msg:
                # 提取图片路径和可能的文字内容
                import re
                image_pattern = r'\[IMAGE:([^\]]+)\]'
                matches = re.findall(image_pattern, msg)
                if matches:
                    image_parts.extend(matches)
                # 移除图片标记，保留文字部分
                text_part = re.sub(image_pattern, '', msg).strip()
                if text_part:
                    text_parts.append(text_part)
            else:
                text_parts.append(msg)

        # 合并文字部分
        combined_text = "\n".join(text_parts).strip()

        # 如果有图片，将图片路径添加到文字后面
        if image_parts:
            # 只保留最后一张图片（通常是最相关的）
            last_image = image_parts[-1]
            if combined_text:
                return f"{combined_text}[IMAGE:{last_image}]"
            else:
                return f"[IMAGE:{last_image}]"

        return combined_text
    
    # 测试用例1: 纯文字消息
    messages1 = ["[2025-07-10 22:35:51]\n你好", "这是第二条消息"]
    result1 = _smart_merge_messages(messages1)
    print(f"测试1 - 纯文字消息合并:")
    print(f"输入: {messages1}")
    print(f"输出: {result1}")
    print()

    # 测试用例2: 文字+图片消息
    messages2 = ["[2025-07-10 22:35:51]\n你看得到这个表情包吗", "[IMAGE:F:\\test\\yuy\\screenshot\\睦子米_20250710223551.png]"]
    result2 = _smart_merge_messages(messages2)
    print(f"测试2 - 文字+图片消息合并:")
    print(f"输入: {messages2}")
    print(f"输出: {result2}")
    print()

    # 测试用例3: 纯图片消息
    messages3 = ["[IMAGE:F:\\test\\yuy\\screenshot\\睦子米_20250710223551.png]"]
    result3 = _smart_merge_messages(messages3)
    print(f"测试3 - 纯图片消息:")
    print(f"输入: {messages3}")
    print(f"输出: {result3}")
    print()

def test_multimodal_llm_prompt():
    """测试多模态LLM提示词优化"""
    print("=" * 50)
    print("测试多模态LLM提示词优化")
    print("=" * 50)

    def _generate_multimodal_prompt(text_content: str) -> str:
        """生成多模态LLM提示词"""
        # 构建专门的图片识别提示词
        image_prompt = ""
        if text_content:
            # 如果有文字内容，说明是文字+图片的组合消息
            image_prompt = f"用户发送了文字消息：{text_content}\n同时还发送了一张图片。请你作为AI助手，根据用户的文字和图片内容进行回复。如果图片是表情包，请理解表情包的含义并结合文字内容回复；如果是其他类型的图片，请描述图片内容并结合文字回复。"
        else:
            # 纯图片消息，判断是否为表情包
            image_prompt = "用户发送了一张图片。如果这是一张表情包，请简洁地理解并回应表情包的含义和情绪，不要说'这是一张表情包'等描述性语言，直接回应表情包想表达的内容；如果是其他类型的图片，请描述图片内容。"

        return image_prompt
    
    # 测试用例1: 文字+图片
    text_content1 = "你看得到这个表情包吗"
    prompt1 = _generate_multimodal_prompt(text_content1)
    print(f"测试1 - 文字+图片提示词:")
    print(f"文字内容: {text_content1}")
    print(f"生成的提示词: {prompt1}")
    print()

    # 测试用例2: 纯图片
    prompt2 = _generate_multimodal_prompt("")
    print(f"测试2 - 纯图片提示词:")
    print(f"生成的提示词: {prompt2}")
    print()

def test_auto_send_timer():
    """测试自动发送消息定时器"""
    print("=" * 50)
    print("测试自动发送消息定时器")
    print("=" * 50)

    # 简化的定时器测试逻辑
    class MockAutoSendHandler:
        def __init__(self):
            self.countdown_timer = None
            self.is_countdown_running = False
            self.last_chat_time = None
            self.unanswered_count = 0

        def start_countdown(self):
            """开始新的倒计时"""
            if self.countdown_timer:
                self.countdown_timer.cancel()
                print("取消了之前的倒计时")

            # 模拟创建新的定时器
            self.countdown_timer = threading.Timer(5.0, lambda: print("倒计时结束"))
            self.countdown_timer.daemon = True
            self.countdown_timer.start()
            self.is_countdown_running = True
            print("创建了新的倒计时")

        def update_last_chat_time(self):
            """更新最后一次聊天时间"""
            from datetime import datetime
            self.last_chat_time = datetime.now()
            self.unanswered_count = 0
            print(f"更新最后聊天时间: {self.last_chat_time}，重置未回复计数器为0")

            # 收到用户消息时，重新开始倒计时
            self.start_countdown()

        def stop(self):
            """停止自动发送消息"""
            if self.countdown_timer:
                self.countdown_timer.cancel()
                self.countdown_timer = None
            self.is_countdown_running = False
            print("自动发送消息已停止")

    auto_sender = MockAutoSendHandler()
    
    print(f"初始状态 - 倒计时运行中: {auto_sender.is_countdown_running}")
    print(f"倒计时器: {auto_sender.countdown_timer}")
    
    # 启动第一个倒计时
    print("\n启动第一个倒计时...")
    auto_sender.start_countdown()
    timer1 = auto_sender.countdown_timer
    print(f"第一个倒计时器ID: {id(timer1)}")
    print(f"倒计时运行中: {auto_sender.is_countdown_running}")
    
    time.sleep(1)
    
    # 模拟收到消息，更新聊天时间（应该重置倒计时）
    print("\n模拟收到消息，更新聊天时间...")
    auto_sender.update_last_chat_time()
    timer2 = auto_sender.countdown_timer
    print(f"第二个倒计时器ID: {id(timer2)}")
    print(f"倒计时器是否相同: {timer1 is timer2}")
    print(f"第一个倒计时器是否已取消: {not timer1.is_alive() if timer1 else 'None'}")
    
    # 清理
    auto_sender.stop()
    print(f"\n停止后 - 倒计时运行中: {auto_sender.is_countdown_running}")

if __name__ == "__main__":
    print("开始测试修复后的功能...")
    print(f"测试时间: {datetime.now()}")
    print()
    
    try:
        test_smart_message_merge()
        test_multimodal_llm_prompt()
        test_auto_send_timer()
        
        print("=" * 50)
        print("所有测试完成！")
        print("=" * 50)
        
    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
