#!/usr/bin/env python3
"""调试TTS设备查找问题"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_tts_device():
    print("=== TTS设备调试 ===\n")
    
    # 1. 检查配置
    print("1. 检查配置...")
    try:
        from src.config import VOICE_CALL_ANTI_ECHO_ENABLED, VOICE_CALL_TTS_DEVICE_KEYWORD
        print(f"   防回音功能: {'启用' if VOICE_CALL_ANTI_ECHO_ENABLED else '禁用'}")
        print(f"   TTS设备关键词: '{VOICE_CALL_TTS_DEVICE_KEYWORD}'")
    except Exception as e:
        print(f"   配置加载失败: {e}")
        return
    
    print()
    
    # 2. 检查可用的音频库
    print("2. 检查音频库...")
    try:
        import sounddevice as sd
        print("   ✅ sounddevice 可用")
        use_sd = True
    except ImportError:
        print("   ❌ sounddevice 不可用")
        try:
            import pyaudio
            print("   ✅ pyaudio 可用")
            use_sd = False
        except ImportError:
            print("   ❌ pyaudio 也不可用")
            return
    
    print()
    
    # 3. 列出所有输出设备
    print("3. 列出所有输出设备...")
    devices = []
    
    if use_sd:
        for idx, dev in enumerate(sd.query_devices()):
            if dev.get("max_output_channels", 0) > 0:
                devices.append((idx, dev.get("name", "")))
                print(f"   {idx}: {dev.get('name', '')} (输出通道: {dev.get('max_output_channels', 0)})")
    else:
        pa = pyaudio.PyAudio()
        for idx in range(pa.get_device_count()):
            info = pa.get_device_info_by_index(idx)
            if info.get("maxOutputChannels", 0) > 0:
                devices.append((idx, info.get("name", "")))
                print(f"   {idx}: {info.get('name', '')} (输出通道: {info.get('maxOutputChannels', 0)})")
        pa.terminate()
    
    print()
    
    # 4. 测试设备匹配
    print("4. 测试设备匹配...")
    keyword = VOICE_CALL_TTS_DEVICE_KEYWORD
    print(f"   查找关键词: '{keyword}'")
    
    # 精确匹配
    exact_match = None
    for device_idx, device_name in devices:
        if keyword.lower() in device_name.lower():
            exact_match = (device_idx, device_name)
            print(f"   ✅ 精确匹配: {device_idx} - {device_name}")
            break
    
    if not exact_match:
        print("   ❌ 没有精确匹配")
        
        # 模糊匹配
        import difflib
        device_names = [name for _, name in devices]
        closest_matches = difflib.get_close_matches(keyword, device_names, n=3, cutoff=0.3)
        
        if closest_matches:
            print(f"   相似匹配候选:")
            for match in closest_matches:
                for device_idx, device_name in devices:
                    if device_name == match:
                        print(f"     {device_idx}: {device_name}")
                        break
        else:
            print("   ❌ 没有相似匹配")
    
    print()
    
    # 5. 测试TTS客户端初始化
    print("5. 测试TTS客户端初始化...")
    try:
        from src.fish.tts_client import TTSClient
        tts = TTSClient()
        print(f"   ✅ TTS客户端初始化成功")
        print(f"   设备关键词: '{tts.device_keyword}'")
        
        # 测试设备查找
        device_idx = tts._find_output_device(tts.device_keyword)
        if device_idx is not None:
            if use_sd:
                dev_name = sd.query_devices(device_idx)["name"]
            else:
                pa_tmp = pyaudio.PyAudio()
                dev_name = pa_tmp.get_device_info_by_index(device_idx)["name"]
                pa_tmp.terminate()
            print(f"   ✅ 找到输出设备: {device_idx} - {dev_name}")
        else:
            print(f"   ❌ 未找到匹配的输出设备")
            
    except Exception as e:
        print(f"   ❌ TTS客户端初始化失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_tts_device()
