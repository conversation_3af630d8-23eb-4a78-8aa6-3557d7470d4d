#!/usr/bin/env python3
"""简单的TTS测试"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_tts_simple():
    print("=== 简单TTS测试 ===\n")
    
    try:
        from src.fish.tts_client import TTSClient
        
        # 初始化TTS客户端
        tts = TTSClient()
        print(f"✅ TTS客户端初始化成功")
        print(f"   设备关键词: {tts.device_keyword}")
        
        # 测试设备查找
        device_idx = tts._find_output_device(tts.device_keyword)
        if device_idx is not None:
            print(f"✅ 找到输出设备: 索引 {device_idx}")
        else:
            print(f"❌ 未找到设备关键词: {tts.device_keyword}")
            return
            
        # 测试简单的TTS调用
        print("\n🎵 测试TTS播放...")
        test_text = "测试"
        tts.say(test_text)
        print("✅ TTS播放完成！")
        
    except Exception as e:
        print(f"❌ TTS测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_tts_simple()
