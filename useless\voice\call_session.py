from __future__ import annotations

"""call_session.py

在一次微信语音通话期间，串联 ASR → LLM → TTS 三个组件，
实现实时语音对话。

• ASR    基于 src.ASR.main.BigASRClient            （火山大模型语音识别）
• LLM    使用 OpenAI ChatCompletion（亦可换成项目中的 LLMService）
• TTS    使用 fish_audio_sdk (封装在 fish.TTSClient)

使用方式：

    from voice.call_session import CallSession
    CallSession().start()   # 非阻塞（内部自建线程）
"""

import os
import queue
import threading
import logging
from typing import Optional
from pathlib import Path
from src.services.ai.llm_service import LLMService
from src.config import config as _cfg

from fish import TTSClient
import re
import emoji
import contextlib
import io
import json, gzip, struct, time  # 新增
from collections import deque

# 导入 ASR 大客户端
try:
    # 优先从相对路径导入（项目内部）
    from src.ASR.main import BigASRClient
except ModuleNotFoundError:  # 兼容 __main__ 跑脚本
    from ASR.main import BigASRClient  # type: ignore

# OpenAI (官方 python sdk)
try:
    from openai import OpenAI
except ImportError:
    OpenAI = None  # type: ignore

# 添加窗口检测相关导入
try:
    import win32gui
    import win32con
    WINDOWS_AVAILABLE = True
except ImportError:
    WINDOWS_AVAILABLE = False

# 统一使用主 logger，确保在前端面板可见
main_logger = logging.getLogger('main')

if not WINDOWS_AVAILABLE:
    main_logger.warning("win32gui 不可用，将使用超时机制作为备用方案")


# ---------------------------------------------------------------------------
# 微信通话窗口检测器 - 简化版本，基于新微信窗口检测
# ---------------------------------------------------------------------------
class WeChatCallWindowDetector:
    """检测微信语音通话窗口状态 - 基于新出现的微信窗口"""

    def __init__(self):
        self.last_check_time = 0
        self.check_interval = 2  # 每2秒检查一次
        self.tracked_call_window_id = None  # 跟踪的通话窗口ID
        self.baseline_wechat_windows = set()  # 基线微信窗口集合
        self.call_started = False

    def get_wechat_windows(self):
        """获取当前所有微信窗口的ID"""
        if not WINDOWS_AVAILABLE:
            return set()

        wechat_windows = set()
        def enum_windows_callback(hwnd, windows_set):
            if win32gui.IsWindowVisible(hwnd):
                try:
                    window_title = win32gui.GetWindowText(hwnd)
                    # 检查是否为微信窗口
                    if window_title == "微信" or "WeChat" in window_title:
                        windows_set.add(hwnd)
                except Exception:
                    pass
            return True

        try:
            win32gui.EnumWindows(enum_windows_callback, wechat_windows)
            return wechat_windows
        except Exception as e:
            main_logger.debug(f"枚举微信窗口时出错: {e}")
            return set()

    def set_baseline(self):
        """设置基线微信窗口集合（在通话开始前调用）"""
        self.baseline_wechat_windows = self.get_wechat_windows()
        main_logger.info(f"已设置基线微信窗口集合，共 {len(self.baseline_wechat_windows)} 个微信窗口")
        main_logger.debug(f"基线微信窗口ID: {list(self.baseline_wechat_windows)}")

    def detect_new_wechat_window(self):
        """检测新出现的微信窗口（通话窗口）"""
        if not WINDOWS_AVAILABLE:
            return None

        current_wechat_windows = self.get_wechat_windows()
        new_wechat_windows = current_wechat_windows - self.baseline_wechat_windows

        if new_wechat_windows:
            # 假设新出现的微信窗口就是通话窗口
            call_window_id = list(new_wechat_windows)[0]  # 取第一个新窗口
            try:
                window_title = win32gui.GetWindowText(call_window_id)
                main_logger.info(f"检测到新的微信窗口（通话窗口）: '{window_title}' (ID: {call_window_id})")
                self.tracked_call_window_id = call_window_id
                self.call_started = True
                return call_window_id, window_title
            except Exception as e:
                main_logger.debug(f"获取新微信窗口标题失败: {e}")
                return None

        return None

    def is_window_alive(self, hwnd):
        """检查指定窗口是否仍然存在"""
        if not WINDOWS_AVAILABLE:
            return False
        try:
            return win32gui.IsWindow(hwnd) and win32gui.IsWindowVisible(hwnd)
        except Exception:
            return False

    def find_call_window(self):
        """查找微信通话窗口（兼容旧接口）"""
        if self.tracked_call_window_id:
            # 如果已经跟踪了特定窗口，检查该窗口是否仍然存在
            if self.is_window_alive(self.tracked_call_window_id):
                try:
                    title = win32gui.GetWindowText(self.tracked_call_window_id)
                    return (self.tracked_call_window_id, title)
                except Exception:
                    main_logger.info(f"跟踪的通话窗口 {self.tracked_call_window_id} 无法获取标题，可能已关闭")
                    self.tracked_call_window_id = None
                    self.call_started = False
                    return None
            else:
                main_logger.info(f"跟踪的通话窗口 {self.tracked_call_window_id} 已关闭")
                self.tracked_call_window_id = None
                self.call_started = False
                return None
        else:
            # 如果没有跟踪窗口，尝试检测新的通话窗口
            return self.detect_new_wechat_window()

    def is_window_alive(self, hwnd):
        """检查指定窗口是否仍然存在"""
        if not WINDOWS_AVAILABLE:
            return False
        try:
            return win32gui.IsWindow(hwnd) and win32gui.IsWindowVisible(hwnd)
        except Exception:
            return False

    def is_call_active(self):
        """检查通话是否仍在进行"""
        current_time = time.time()

        # 限制检查频率
        if current_time - self.last_check_time < self.check_interval:
            return self.call_started  # 返回上次的状态，避免频繁检查

        self.last_check_time = current_time

        if self.tracked_call_window_id:
            # 直接检查跟踪的窗口是否存在
            if self.is_window_alive(self.tracked_call_window_id):
                main_logger.debug(f"通话窗口 {self.tracked_call_window_id} 仍然存在")
                return True
            else:
                main_logger.info(f"通话窗口 {self.tracked_call_window_id} 已关闭，通话结束")
                self.tracked_call_window_id = None
                self.call_started = False
                return False
        else:
            # 尝试检测新的通话窗口
            new_window = self.detect_new_wechat_window()
            return new_window is not None

    def start_call_detection(self):
        """开始通话检测（在发起通话前调用）"""
        main_logger.info("开始通话检测，设置基线微信窗口...")
        self.set_baseline()
        self.tracked_call_window_id = None
        self.call_started = False

    def stop_call_detection(self):
        """停止通话检测"""
        main_logger.info("停止通话检测")
        self.tracked_call_window_id = None
        self.call_started = False
        self.baseline_wechat_windows.clear()

# ---------------------------------------------------------------------------
# ASR 桥接: 将 2 秒总结结果推入队列
# ---------------------------------------------------------------------------
class _ASRBridge(BigASRClient):
    def __init__(self, out_q: "queue.Queue[str]", recent_tts: deque[str]):
        super().__init__()
        self._out_q = out_q
        self._recent_tts = recent_tts

    # ---------- 重载父类，取消 print，仅推送队列 ----------
    def _print_summary(self):  # type: ignore[override]
        if not self._latest_full_text:
            return
        if len(self._latest_full_text) >= self._summary_index:
            segment = self._latest_full_text[self._summary_index:]
        else:
            segment = self._latest_full_text
        if segment.strip():
            seg_clean = segment.strip()
            if seg_clean:
                # ---- 回声抑制：若与最近 TTS 高度重合，则忽略 ----
                is_echo = False
                for t in self._recent_tts:
                    if len(t) < 4:
                        continue
                    if t in seg_clean or seg_clean in t:
                        is_echo = True
                        break
                if is_echo:
                    main_logger.debug("[EchoSkip] %s", seg_clean)
                else:
                    main_logger.info("[ASR->LLM] %s", seg_clean)
                    self._out_q.put(seg_clean)
            self._summary_index = len(self._latest_full_text)
            self._last_printed = self._latest_full_text

    # ---------- 将 2 秒静默改为 5 秒 ----------
    def _reset_summary_timer(self):  # type: ignore[override]
        if self._summary_timer:
            self._summary_timer.cancel()
        self._summary_timer = threading.Timer(5.0, self._print_summary)
        self._summary_timer.daemon = True
        self._summary_timer.start()

    # ---------- 静默父类 noisy 输出 ----------
    def _suppress_stdout(self, func, *args, **kwargs):
        with contextlib.redirect_stdout(io.StringIO()):
            return func(*args, **kwargs)

    def _on_open(self, ws):  # type: ignore[override]
        self._suppress_stdout(super()._on_open, ws)

    def _on_error(self, ws, error):  # type: ignore[override]
        self._suppress_stdout(super()._on_error, ws, error)

    def _on_close(self, ws, status_code, msg):  # type: ignore[override]
        self._suppress_stdout(super()._on_close, ws, status_code, msg)

    def _on_message(self, ws, message):  # type: ignore[override]
        """调用父类 _on_message 并捕获其 stdout 增量，转为日志。"""
        buf = io.StringIO()
        with contextlib.redirect_stdout(buf):
            super()._on_message(ws, message)
        out = buf.getvalue()
        if out.strip():
            for line in out.strip().splitlines():
                main_logger.info("[ASR] %s", line.strip())

    # 修改 _print_summary 日志前缀
        

# ---------------------------------------------------------------------------
# CallSession
# ---------------------------------------------------------------------------
class CallSession:
    """贯穿一次通话周期的会话管理器。"""

    def __init__(
        self,
        llm_model: str | None = None,
    ) -> None:
        # 队列
        self._asr_q: "queue.Queue[str]" = queue.Queue()
        self._tts_q: "queue.Queue[str]" = queue.Queue()
        self._recent_tts: deque[str] = deque(maxlen=6)

        # 事件
        self._stopped = threading.Event()
        self._tts_playing = threading.Event()  # TTS播放状态标志

        # 组件
        self._asr = _ASRBridge(self._asr_q, self._recent_tts)
        self._tts = TTSClient()

        # 窗口检测器
        self._window_detector = WeChatCallWindowDetector()
        self._max_call_duration = 3600  # 最大通话时长1小时（秒）
        self._call_start_time = None

        # 初始化 OpenAI 客户端（若可用）
        self._openai = None
        self._llm_model = ""
        if OpenAI is not None:
            # 1) 优先从环境变量读取
            api_key = os.getenv("OPENAI_API_KEY", "")
            base_url = os.getenv("OPENAI_API_BASE", "")
            model_from_env = os.getenv("OPENAI_MODEL", "")

            # 2) 若环境变量为空，尝试从项目 config 中读取
            if not api_key:
                try:
                    from src.config import config as _cfg  # type: ignore
                    api_key = _cfg.llm.api_key or ""
                    base_url = _cfg.llm.base_url or base_url
                    model_from_env = _cfg.llm.model or model_from_env
                except Exception:
                    pass  # 保持为空

            # 3) 若仍无 API Key，则关闭 LLM 功能
            if not api_key:
                main_logger.warning("未找到 OpenAI API Key，LLM 功能已禁用")
            else:
                kwargs: dict[str, str] = {"api_key": api_key}
                if base_url:
                    kwargs["base_url"] = base_url
                self._openai = OpenAI(**kwargs)  # type: ignore
                self._llm_model = llm_model or model_from_env or "gpt-3.5-turbo"

        # 初始化 LLMService（优先使用项目配置）
        try:
            avatar_dir = Path(_cfg.behavior.context.avatar_dir)
            # 支持两套路径：项目根 /kourichat/ 以及工作目录
            possible_root = Path(__file__).resolve().parents[3]
            prompt_path = (possible_root / avatar_dir / "avatar.md").resolve()
            if not prompt_path.exists():
                prompt_path = Path.cwd() / avatar_dir / "avatar.md"
            system_prompt = prompt_path.read_text(encoding="utf-8") if prompt_path.exists() else "你是一个语音助手。"

            self._llm_service = LLMService(
                api_key=_cfg.llm.api_key,
                base_url=_cfg.llm.base_url,
                model=_cfg.llm.model,
                max_token=_cfg.llm.max_tokens,
                temperature=_cfg.llm.temperature,
                max_groups=_cfg.behavior.context.max_groups,
            )
            self._system_prompt = system_prompt
        except Exception as e:
            main_logger.error("初始化 LLMService 失败: %s", e)
            self._llm_service = None
            self._system_prompt = "你是一个语音助手。"

        # 预编译 TTS 清洗正则
        self._bracket_tag_re = re.compile(r"\[[^\]]+\]")

    # ---------------------------- 公共接口 ----------------------------
    def start(self) -> None:
        """后台线程方式启动所有组件（返回即代表主线程可继续）。"""
        # 记录通话开始时间
        self._call_start_time = time.time()

        # 注意：不在这里设置基线，因为应该在拨打电话前就设置好了

        # ASR 单独线程（阻塞 run_forever）
        threading.Thread(target=self._asr.start, name="ASR-Thread", daemon=True).start()

        # TTS 队列消费
        threading.Thread(target=self._run_tts, name="TTS-Thread", daemon=True).start()

        # LLM 队列消费
        threading.Thread(target=self._run_llm, name="LLM-Thread", daemon=True).start()

        # 通话状态监控线程
        threading.Thread(target=self._monitor_call_status, name="CallMonitor-Thread", daemon=True).start()

        main_logger.info("CallSession 已启动 (ASR+LLM+TTS+CallMonitor)")

    def stop(self):
        self._stopped.set()
        # 停止通话检测
        self._window_detector.stop_call_detection()

        # 停止队列处理
        main_logger.info("正在停止队列处理...")
        self._asr_q.put("__STOP__")
        self._tts_q.put("__STOP__")

        # 最小化ASR停止操作，避免影响全局状态
        def minimal_asr_cleanup():
            import time
            time.sleep(0.5)  # 短暂等待
            try:
                main_logger.info("正在进行最小化ASR清理...")

                # 只关闭WebSocket连接，不触及音频设备
                if hasattr(self._asr, 'ws') and self._asr.ws:
                    try:
                        self._asr.ws.close()
                        main_logger.info("ASR WebSocket连接已关闭")
                    except Exception as e:
                        main_logger.debug(f"关闭ASR WebSocket时出错: {e}")

                # 取消计时器
                if hasattr(self._asr, '_summary_timer') and self._asr._summary_timer:
                    try:
                        self._asr._summary_timer.cancel()
                        main_logger.info("ASR计时器已取消")
                    except Exception as e:
                        main_logger.debug(f"取消ASR计时器时出错: {e}")

                main_logger.info("ASR最小化清理完成")

            except Exception as e:
                main_logger.debug(f"ASR清理时出错: {e}")

        # 在后台线程中进行最小化清理
        import threading
        threading.Thread(target=minimal_asr_cleanup, daemon=True).start()

        main_logger.info("CallSession 已停止")

    # ---------------------------- 私有 ----------------------------
    def _monitor_call_status(self):
        """监控通话状态，自动检测通话结束"""
        main_logger.info("通话状态监控已启动")

        while not self._stopped.is_set():
            try:
                # 检查超时
                if self._call_start_time and time.time() - self._call_start_time > self._max_call_duration:
                    main_logger.warning(f"通话超时（{self._max_call_duration}秒），自动停止")
                    self.stop()
                    break

                # 检查通话窗口是否仍然存在
                if self._window_detector.tracked_call_window_id:
                    if not self._window_detector.is_window_alive(self._window_detector.tracked_call_window_id):
                        main_logger.info(f"检测到通话窗口 {self._window_detector.tracked_call_window_id} 已关闭，自动停止CallSession")
                        self.stop()
                        break
                    else:
                        main_logger.debug(f"通话窗口 {self._window_detector.tracked_call_window_id} 仍然存在")
                else:
                    # 如果还没有跟踪到通话窗口，尝试检测
                    new_window = self._window_detector.detect_new_wechat_window()
                    if new_window:
                        hwnd, title = new_window
                        main_logger.info(f"监控线程检测到通话窗口: {title} (ID: {hwnd})")

                # 等待一段时间再检查
                time.sleep(2)

            except Exception as e:
                main_logger.error(f"监控通话状态时出错: {e}")
                time.sleep(5)  # 出错时等待更长时间

        main_logger.info("通话状态监控已退出")
    def _clean_for_tts(self, text: str) -> str:
        """去掉 [love]、[happy] 及所有 emoji，避免 TTS 读出。"""
        no_tags = self._bracket_tag_re.sub("", text)
        no_emoji = emoji.replace_emoji(no_tags, replace="")
        return no_emoji.strip()

    def _run_tts(self):
        while not self._stopped.is_set():
            text = self._tts_q.get()
            if text == "__STOP__":
                break
            try:
                main_logger.debug("TTS ← %s", text)
                self._tts.say(text)
                # 保存最近播报，用于回声抑制
                self._recent_tts.append(text)
            except Exception as e:
                main_logger.error("TTS 播放失败: %s", e)

    def _run_llm(self):
        while not self._stopped.is_set():
            user_text = self._asr_q.get()
            if user_text == "__STOP__":
                break
            main_logger.debug("LLM 用户文本: %s", user_text)
            try:
                if self._llm_service:
                    reply = self._llm_service.get_response(
                        message=user_text,
                        user_id="voice_call",
                        system_prompt=self._system_prompt,
                    )
                elif self._openai:
                    resp = self._openai.chat.completions.create(
                        model=self._llm_model,
                        messages=[{"role": "user", "content": user_text}],
                        max_tokens=256,
                        temperature=0.7,
                    )
                    reply = resp.choices[0].message.content.strip()
                else:
                    reply = "(LLM 未配置)"
                reply_clean = self._clean_for_tts(reply)
                main_logger.debug("LLM 回复(清洗后): %s", reply_clean)
                self._tts_q.put(reply_clean)
            except Exception as e:
                main_logger.error("调用 LLM 失败: %s", e)


# ------------------ CLI 方便调试 ------------------
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format="[%(asctime)s] %(levelname)s: %(message)s")
    session = CallSession()
    session.start()
    # 保持主线程存活
    try:
        while True:
            threading.Event().wait(1)
    except KeyboardInterrupt:
        session.stop()
        main_logger.info("通话会话已停止") 