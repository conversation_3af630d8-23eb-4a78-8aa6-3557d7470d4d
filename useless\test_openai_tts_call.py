#!/usr/bin/env python3
"""测试OpenAI TTS调用"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_openai_tts():
    print("=== OpenAI TTS调用测试 ===\n")
    
    try:
        from src.fish.tts_client import TTSClient
        
        # 初始化TTS客户端
        tts = TTSClient()
        print(f"✅ TTS客户端初始化成功")
        print(f"   API Key: {tts.api_key[:10]}...")
        print(f"   Base URL: {tts.base_url}")
        print(f"   Model: {tts.model}")
        print(f"   Voice: {tts.voice}")
        print(f"   是否Fish Audio: {tts.is_fish_audio}")
        
        # 测试简单的TTS调用
        print("\n🎵 测试TTS播放...")
        test_text = "Hello world"
        tts.say(test_text)
        print("✅ TTS播放成功！")
        
    except Exception as e:
        print(f"❌ TTS测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_openai_tts()
