<!DOCTYPE html>
<html>
<head>
    <title>配置API测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .config-group { margin-bottom: 20px; border: 1px solid #ccc; padding: 10px; }
        .config-item { margin: 5px 0; }
        .secret { color: #999; }
    </style>
</head>
<body>
    <h1>配置API测试</h1>
    <div id="configs"></div>

    <script>
        async function loadConfigs() {
            try {
                const response = await fetch('/get_all_configs');
                const data = await response.json();
                
                console.log('配置数据:', data);
                
                const configsDiv = document.getElementById('configs');
                
                if (data.status === 'success') {
                    for (const [groupName, groupConfigs] of Object.entries(data.configs)) {
                        const groupDiv = document.createElement('div');
                        groupDiv.className = 'config-group';
                        
                        const groupTitle = document.createElement('h3');
                        groupTitle.textContent = groupName;
                        groupDiv.appendChild(groupTitle);
                        
                        for (const [configKey, configValue] of Object.entries(groupConfigs)) {
                            const itemDiv = document.createElement('div');
                            itemDiv.className = 'config-item';
                            
                            const displayValue = configValue.is_secret ? 
                                (configValue.value ? '***隐藏***' : '未设置') : 
                                configValue.value;
                            
                            itemDiv.innerHTML = `
                                <strong>${configKey}:</strong> 
                                <span class="${configValue.is_secret ? 'secret' : ''}">${displayValue}</span>
                                <br><small>${configValue.description}</small>
                            `;
                            
                            groupDiv.appendChild(itemDiv);
                        }
                        
                        configsDiv.appendChild(groupDiv);
                    }
                } else {
                    configsDiv.innerHTML = '<p>配置加载失败: ' + data.message + '</p>';
                }
            } catch (error) {
                console.error('加载配置失败:', error);
                document.getElementById('configs').innerHTML = '<p>加载配置失败: ' + error.message + '</p>';
            }
        }
        
        // 页面加载完成后获取配置
        document.addEventListener('DOMContentLoaded', loadConfigs);
    </script>
</body>
</html>
