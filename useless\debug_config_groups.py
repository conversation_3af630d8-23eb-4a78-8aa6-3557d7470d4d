#!/usr/bin/env python3
"""调试配置分组加载情况"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_config_groups():
    print("=== 配置分组调试 ===\n")
    
    try:
        from src.config import config
        print("✅ 配置加载成功")
        print(f"OpenAI TTS API Key: {config.media.openai_tts.api_key[:10]}...")
        print(f"OpenAI TTS Base URL: {config.media.openai_tts.base_url}")
        print(f"OpenAI TTS Model: {config.media.openai_tts.model}")
        print(f"OpenAI TTS Voice: {config.media.openai_tts.voice}")
        print()
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return
    
    # 模拟WebUI配置分组逻辑
    try:
        config_groups = {
            "基础配置": {},
            "图像识别API配置": {},
            "OpenAI TTS配置": {},
            "语音通话配置": {},
            "主动消息配置": {},
            "消息配置": {},
            "人设配置": {},
            "网络搜索配置": {},
            "天气配置": {},
        }

        # OpenAI TTS配置
        config_groups["OpenAI TTS配置"].update(
            {
                "OPENAI_TTS_API_KEY": {
                    "value": config.media.openai_tts.api_key,
                    "description": "OpenAI兼容TTS API密钥",
                    "is_secret": True
                },
                "OPENAI_TTS_BASE_URL": {
                    "value": config.media.openai_tts.base_url,
                    "description": "OpenAI兼容TTS API基础URL"
                },
                "OPENAI_TTS_MODEL": {
                    "value": config.media.openai_tts.model,
                    "description": "TTS模型名称或ID"
                },
                "OPENAI_TTS_VOICE": {
                    "value": config.media.openai_tts.voice,
                    "description": "语音音色名称或ID"
                },
                "OPENAI_TTS_TEMPERATURE": {
                    "value": float(config.media.openai_tts.temperature),
                    "description": "TTS 温度参数",
                    "type": "number",
                    "min": 0.0,
                    "max": 1.0
                },
                "OPENAI_TTS_TOP_P": {
                    "value": float(config.media.openai_tts.top_p),
                    "description": "TTS Top-P参数",
                    "type": "number",
                    "min": 0.0,
                    "max": 1.0
                },
                "OPENAI_TTS_SPEED": {
                    "value": float(config.media.openai_tts.speed),
                    "description": "TTS 语速",
                    "type": "number",
                    "min": 0.5,
                    "max": 2.0
                }
            }
        )
        
        print("✅ 配置分组创建成功")
        print(f"OpenAI TTS配置项数量: {len(config_groups['OpenAI TTS配置'])}")
        
        for key, value in config_groups["OpenAI TTS配置"].items():
            print(f"  {key}: {value['value']}")
        
    except Exception as e:
        print(f"❌ 配置分组创建失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_config_groups()
