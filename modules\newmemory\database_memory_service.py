"""
数据库记忆服务
基于SQLite数据库的新记忆系统实现
"""

import os
import sqlite3
import logging
import json
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from src.services.ai.llm_service import LLMService

logger = logging.getLogger('main')

class DatabaseMemoryService:
    """
    数据库记忆服务
    功能：
    1. 基于SQLite的记忆存储
    2. 自动记忆总结和归档
    3. 智能记忆查询
    4. 与MD文件同步
    """
    
    def __init__(self, root_dir: str, api_key: str, base_url: str, model: str, max_token: int, temperature: float, max_groups: int = 10):
        self.root_dir = root_dir
        self.api_key = api_key
        self.base_url = base_url
        self.model = model
        self.max_token = max_token
        self.temperature = temperature
        self.max_groups = max_groups
        self.conversation_count = {}

        # 缓存相关配置
        self.cache = {}  # 缓存存储
        self.cache_expire_time = 300  # 缓存过期时间（5分钟）
        self.max_cache_entries = 100  # 最大缓存条目数

        # 初始化LLM服务
        self.llm_service = LLMService(
            api_key=api_key,
            base_url=base_url,
            model=model,
            max_token=max_token,
            temperature=temperature,
            max_groups=max_groups
        )

        # 初始化数据库表
        self._initialize_database_tables()

    def _get_cache_key(self, avatar_name: str, user_id: str) -> str:
        """生成缓存键"""
        return f"{avatar_name}_{user_id}"

    def _is_cache_valid(self, cache_entry: Dict) -> bool:
        """检查缓存是否有效"""
        if not cache_entry:
            return False
        current_time = time.time()
        return current_time - cache_entry.get('timestamp', 0) < self.cache_expire_time

    def _clean_expired_cache(self):
        """清理过期的缓存条目"""
        current_time = time.time()
        expired_keys = []

        for key, entry in self.cache.items():
            if current_time - entry.get('timestamp', 0) >= self.cache_expire_time:
                expired_keys.append(key)

        for key in expired_keys:
            del self.cache[key]

        # 如果缓存条目过多，删除最旧的条目
        if len(self.cache) > self.max_cache_entries:
            sorted_cache = sorted(self.cache.items(), key=lambda x: x[1].get('timestamp', 0))
            for key, _ in sorted_cache[:len(self.cache) - self.max_cache_entries]:
                del self.cache[key]

    def _update_cache(self, avatar_name: str, user_id: str, short_memory: List[Dict], core_memory: str):
        """更新缓存"""
        cache_key = self._get_cache_key(avatar_name, user_id)
        self.cache[cache_key] = {
            'short_memory': short_memory,
            'core_memory': core_memory,
            'timestamp': time.time()
        }

        # 清理过期缓存
        self._clean_expired_cache()

    def _get_from_cache(self, avatar_name: str, user_id: str) -> Optional[Dict]:
        """从缓存获取数据"""
        cache_key = self._get_cache_key(avatar_name, user_id)
        cache_entry = self.cache.get(cache_key)

        if self._is_cache_valid(cache_entry):
            return cache_entry

        # 缓存无效，删除
        if cache_key in self.cache:
            del self.cache[cache_key]

        return None

    def _invalidate_cache(self, avatar_name: str, user_id: str):
        """使缓存失效"""
        cache_key = self._get_cache_key(avatar_name, user_id)
        if cache_key in self.cache:
            del self.cache[cache_key]
    
    def _get_avatar_newmemory_dir(self, avatar_name: str) -> str:
        """获取角色新记忆目录"""
        memory_dir = os.path.join(self.root_dir, "data", "avatars", avatar_name, "memory")
        os.makedirs(memory_dir, exist_ok=True)
        os.makedirs(os.path.join(memory_dir, "db"), exist_ok=True)
        os.makedirs(os.path.join(memory_dir, "md"), exist_ok=True)
        os.makedirs(os.path.join(memory_dir, "archived_memory"), exist_ok=True)
        return memory_dir
    
    def _get_core_memory_db_path(self, avatar_name: str) -> str:
        """获取核心记忆数据库路径"""
        memory_dir = self._get_avatar_newmemory_dir(avatar_name)
        return os.path.join(memory_dir, "db", "core_memory.db")
    
    def _get_short_memory_db_path(self, avatar_name: str) -> str:
        """获取短期记忆数据库路径"""
        memory_dir = self._get_avatar_newmemory_dir(avatar_name)
        return os.path.join(memory_dir, "db", "short_memory.db")
    
    def add_conversation(self, avatar_name: str, user_message: str, bot_reply: str, user_id: str, is_system_message: bool = False):
        """
        添加对话到短期记忆数据库
        
        Args:
            avatar_name: 角色名称
            user_message: 用户消息
            bot_reply: 机器人回复
            user_id: 用户ID
            is_system_message: 是否为系统消息
        """
        # 跳过系统消息和错误消息
        if is_system_message or bot_reply.startswith("Error:"):
            return
        
        try:
            # 获取数据库路径
            db_path = self._get_short_memory_db_path(avatar_name)
            
            # 连接数据库
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 获取当前周数
            current_week = datetime.now().isocalendar()[1]
            
            # 插入对话记录
            cursor.execute('''
                INSERT INTO short_memory (user_id, user_message, bot_reply, timestamp, week_number)
                VALUES (?, ?, ?, ?, ?)
            ''', (user_id, user_message, bot_reply, datetime.now(), current_week))
            
            conn.commit()
            conn.close()
            
            # 更新对话计数
            conversation_key = f"{avatar_name}_{user_id}"
            if conversation_key not in self.conversation_count:
                self.conversation_count[conversation_key] = 0
            
            self.conversation_count[conversation_key] += 1
            
            # 每10轮对话更新一次核心记忆
            if self.conversation_count[conversation_key] >= 10:
                logger.info(f"角色 {avatar_name} 为用户 {user_id} 达到10轮对话，开始更新核心记忆")
                context = self.get_recent_context(avatar_name, user_id)
                self.update_core_memory(avatar_name, user_id, context)
                self.conversation_count[conversation_key] = 0
            
            # 检查是否需要归档（每周检查一次）
            self._check_and_archive_memory(avatar_name, user_id)

            # 同步短期记忆到MD文件
            self._sync_short_memory_to_md(avatar_name, user_id)

            # 使缓存失效，强制下次查询时重新从数据库获取
            self._invalidate_cache(avatar_name, user_id)

            logger.info(f"对话已添加到数据库: 角色={avatar_name}, 用户ID={user_id}")

        except Exception as e:
            logger.error(f"添加对话到数据库失败: {e}")

    def get_comprehensive_memory(self, avatar_name: str, user_id: str, short_memory_limit: int = 10) -> Dict:
        """
        获取综合记忆数据（短期记忆 + 核心记忆）
        优先从缓存获取，缓存未命中时从数据库查询

        Args:
            avatar_name: 角色名称
            user_id: 用户ID
            short_memory_limit: 短期记忆查询数量限制

        Returns:
            Dict: 包含短期记忆和核心记忆的字典
        """
        try:
            # 先尝试从缓存获取
            cached_data = self._get_from_cache(avatar_name, user_id)
            if cached_data:
                logger.info(f"✓ 缓存命中 - 角色={avatar_name}, 用户ID={user_id}, 短期记忆={len(cached_data['short_memory'])}条")
                return {
                    'short_memory': cached_data['short_memory'],
                    'core_memory': cached_data['core_memory']
                }

            # 缓存未命中，从数据库查询
            logger.info(f"✗ 缓存未命中，查询数据库 - 角色={avatar_name}, 用户ID={user_id}")

            # 查询短期记忆（最近10条）
            short_memory = self._get_recent_short_memory(avatar_name, user_id, short_memory_limit)

            # 查询全部核心记忆
            core_memory = self._get_all_core_memory(avatar_name, user_id)

            # 更新缓存
            self._update_cache(avatar_name, user_id, short_memory, core_memory)
            logger.info(f"✓ 缓存已更新 - 角色={avatar_name}, 用户ID={user_id}, 短期记忆={len(short_memory)}条")

            return {
                'short_memory': short_memory,
                'core_memory': core_memory
            }

        except Exception as e:
            logger.error(f"获取综合记忆数据失败: {e}")
            return {
                'short_memory': [],
                'core_memory': ""
            }

    def _get_recent_short_memory(self, avatar_name: str, user_id: str, limit: int = 10) -> List[Dict]:
        """
        获取最近的短期记忆

        Args:
            avatar_name: 角色名称
            user_id: 用户ID
            limit: 查询数量限制

        Returns:
            List[Dict]: 短期记忆列表
        """
        try:
            db_path = self._get_short_memory_db_path(avatar_name)

            if not os.path.exists(db_path):
                return []

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 查询最近的对话记录
            cursor.execute('''
                SELECT user_message, bot_reply, timestamp FROM short_memory
                WHERE user_id = ? OR user_id = '*'
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (user_id, limit))

            results = cursor.fetchall()
            conn.close()

            # 转换为标准格式
            memory_list = []
            for user_message, bot_reply, timestamp in reversed(results):  # 反转以保持时间顺序
                memory_list.append({
                    "role": "user",
                    "content": user_message,
                    "timestamp": timestamp
                })
                memory_list.append({
                    "role": "assistant",
                    "content": bot_reply,
                    "timestamp": timestamp
                })

            return memory_list

        except Exception as e:
            logger.error(f"获取短期记忆失败: {e}")
            return []

    def _get_all_core_memory(self, avatar_name: str, user_id: str) -> str:
        """
        获取全部核心记忆

        Args:
            avatar_name: 角色名称
            user_id: 用户ID

        Returns:
            str: 核心记忆内容
        """
        try:
            db_path = self._get_core_memory_db_path(avatar_name)

            if not os.path.exists(db_path):
                return ""

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 查询用户的全部核心记忆
            cursor.execute('''
                SELECT content, category, importance FROM core_memory
                WHERE user_id = ?
                ORDER BY importance DESC, updated_at DESC
            ''', (user_id,))

            results = cursor.fetchall()
            conn.close()

            if results:
                # 按类别和重要性组织核心记忆内容
                memory_sections = []
                for content, category, importance in results:
                    memory_sections.append(f"[{category}] {content}")

                return "\n\n".join(memory_sections)

            return ""

        except Exception as e:
            logger.error(f"获取核心记忆失败: {e}")
            return ""

    def get_recent_context(self, avatar_name: str, user_id: str, context_size: int = None) -> List[Dict]:
        """
        获取最近的对话上下文
        
        Args:
            avatar_name: 角色名称
            user_id: 用户ID
            context_size: 上下文大小（轮数）
        
        Returns:
            List[Dict]: 对话上下文列表
        """
        try:
            if context_size is None:
                context_size = self.max_groups
            
            db_path = self._get_short_memory_db_path(avatar_name)
            
            if not os.path.exists(db_path):
                return []
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 查询最近的对话记录，支持特定用户ID和通用用户ID（'*'）
            cursor.execute('''
                SELECT user_message, bot_reply FROM short_memory
                WHERE user_id = ? OR user_id = '*'
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (user_id, context_size))  # 直接使用context_size，因为每条记录就是一轮对话
            
            results = cursor.fetchall()
            conn.close()
            
            # 转换为LLM接口要求的格式
            context = []
            for user_message, bot_reply in reversed(results):  # 反转以保持时间顺序
                context.append({"role": "user", "content": user_message})
                context.append({"role": "assistant", "content": bot_reply})
            
            return context
            
        except Exception as e:
            logger.error(f"获取最近对话上下文失败: {e}")
            return []
    
    def get_core_memory(self, avatar_name: str, user_id: str) -> str:
        """
        获取核心记忆内容
        
        Args:
            avatar_name: 角色名称
            user_id: 用户ID
        
        Returns:
            str: 核心记忆内容
        """
        try:
            db_path = self._get_core_memory_db_path(avatar_name)
            
            if not os.path.exists(db_path):
                return ""
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 查询用户的核心记忆
            cursor.execute('''
                SELECT content FROM core_memory
                WHERE user_id = ?
                ORDER BY importance DESC, updated_at DESC
            ''', (user_id,))
            
            results = cursor.fetchall()
            conn.close()
            
            if results:
                # 合并所有核心记忆内容
                memory_content = "\n\n".join([result[0] for result in results])
                return memory_content
            
            return ""
            
        except Exception as e:
            logger.error(f"获取核心记忆失败: {e}")
            return ""
    
    def update_core_memory(self, avatar_name: str, user_id: str, context: List[Dict]) -> bool:
        """
        更新核心记忆
        
        Args:
            avatar_name: 角色名称
            user_id: 用户ID
            context: 对话上下文
        
        Returns:
            bool: 更新是否成功
        """
        try:
            if not context:
                return False
            
            # 获取现有核心记忆
            existing_memory = self.get_core_memory(avatar_name, user_id)
            
            # 构建提示词
            prompt = self._build_memory_prompt()
            
            # 调用LLM生成新的核心记忆
            new_memory = self._generate_core_memory(prompt, existing_memory, context, user_id)
            
            if not new_memory or 'Error' in new_memory:
                logger.warning("生成核心记忆失败，保留原有记忆")
                return False
            
            # 保存到数据库
            db_path = self._get_core_memory_db_path(avatar_name)
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 删除旧的核心记忆
            cursor.execute('DELETE FROM core_memory WHERE user_id = ?', (user_id,))
            
            # 插入新的核心记忆
            cursor.execute('''
                INSERT INTO core_memory (user_id, content, category, importance)
                VALUES (?, ?, ?, ?)
            ''', (user_id, new_memory, 'general', 1))
            
            conn.commit()
            conn.close()
            
            # 同步到MD文件
            self._sync_core_memory_to_md(avatar_name, user_id, new_memory)

            # 使缓存失效，强制下次查询时重新从数据库获取
            self._invalidate_cache(avatar_name, user_id)

            logger.info(f"核心记忆更新成功: 角色={avatar_name}, 用户ID={user_id}")
            return True
            
        except Exception as e:
            logger.error(f"更新核心记忆失败: {e}")
            return False
    
    def _build_memory_prompt(self) -> str:
        """构建记忆生成提示词并替换时间变量"""
        try:
            prompt_path = os.path.join(self.root_dir, 'data/base/memory.md')

            # 检查文件是否存在
            if not os.path.exists(prompt_path):
                logger.warning(f"记忆提示词文件不存在: {prompt_path}")
                return "请根据对话内容生成简洁的核心记忆摘要。"

            with open(prompt_path, 'r', encoding='utf-8') as f:
                prompt_template = f.read()

            # 获取当前时间信息
            from datetime import datetime
            now = datetime.now()
            current_date = now.strftime("%m月%d日")  # 简短格式：8月16日
            current_time = now.strftime("%H:%M")
            weekday_map = {
                'Monday': '星期一', 'Tuesday': '星期二', 'Wednesday': '星期三',
                'Thursday': '星期四', 'Friday': '星期五', 'Saturday': '星期六', 'Sunday': '星期日'
            }
            weekday = weekday_map.get(now.strftime("%A"), now.strftime("%A"))

            # 替换时间变量，使用安全的格式化方法
            try:
                prompt = prompt_template.format(
                    current_date=current_date,
                    current_time=current_time,
                    weekday=weekday
                )
            except KeyError as ke:
                logger.error(f"记忆提示词模板中存在未定义的变量: {ke}")
                # 如果格式化失败，返回原始模板
                prompt = prompt_template
            except ValueError as ve:
                logger.error(f"记忆提示词模板格式错误: {ve}")
                # 如果格式化失败，返回原始模板
                prompt = prompt_template

            return prompt

        except FileNotFoundError:
            logger.error(f"记忆提示词文件未找到: {prompt_path}")
            return "请根据对话内容生成简洁的核心记忆摘要。"
        except UnicodeDecodeError:
            logger.error(f"记忆提示词文件编码错误: {prompt_path}")
            return "请根据对话内容生成简洁的核心记忆摘要。"
        except Exception as e:
            logger.error(f"读取记忆提示词失败: {e}")
            return "请根据对话内容生成简洁的核心记忆摘要。"
    
    def _generate_core_memory(self, prompt: str, existing_memory: str, context: List[Dict], user_id: str) -> str:
        """生成核心记忆"""
        try:
            # 获取当前时间信息
            from datetime import datetime
            now = datetime.now()
            current_date_short = now.strftime("%m月%d日")  # 简短格式：8月16日
            current_time = now.strftime("%H:%M")
            weekday_map = {
                'Monday': '星期一', 'Tuesday': '星期二', 'Wednesday': '星期三',
                'Thursday': '星期四', 'Friday': '星期五', 'Saturday': '星期六', 'Sunday': '星期日'
            }
            weekday = weekday_map.get(now.strftime("%A"), now.strftime("%A"))

            # 构建包含详细时间信息的消息
            detailed_message = f"""请根据设定和要求，生成新的核心记忆。

当前时间信息：
- 今天：{current_date_short} {weekday}
- 现在时间：{current_time}

现有的核心记忆为：
{existing_memory}

最近的对话上下文：
{self._format_context_for_memory(context)}

请特别注意：如果对话中涉及时间相关的约定或计划（如"明天"、"后天"、"下周"等），请根据今天是{current_date_short} {weekday}来计算具体日期，用自然简洁的方式记录，如"明天8月17日要出去玩"。"""

            response = self.llm_service.get_response(
                message=detailed_message,
                user_id=user_id,
                system_prompt=prompt,
                core_memory=existing_memory,
                previous_context=context
            )
            return response
        except Exception as e:
            logger.error(f"生成核心记忆失败: {e}")
            return ""

    def _format_context_for_memory(self, context: List[Dict]) -> str:
        """
        格式化对话上下文，便于记忆生成

        Args:
            context: 对话上下文列表

        Returns:
            str: 格式化后的对话内容
        """
        if not context:
            return "无对话上下文"

        formatted_lines = []
        for item in context:
            if isinstance(item, dict):
                if item.get("role") == "user":
                    formatted_lines.append(f"用户：{item.get('content', '')}")
                elif item.get("role") == "assistant":
                    formatted_lines.append(f"助手：{item.get('content', '')}")
                    formatted_lines.append("---")

        return "\n".join(formatted_lines)

    def _sync_core_memory_to_md(self, avatar_name: str, user_id: str, memory_content: str):
        """将核心记忆同步到MD文件"""
        try:
            memory_dir = self._get_avatar_newmemory_dir(avatar_name)
            md_path = os.path.join(memory_dir, "md", "core_memory.md")
            
            # 构建MD文件内容
            md_content = f"""# 核心记忆

## 用户: {user_id}

{memory_content}

---
*最后更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
*记录状态: 自动更新*
"""
            
            with open(md_path, 'w', encoding='utf-8') as f:
                f.write(md_content)
            
            logger.debug(f"核心记忆已同步到MD文件: {md_path}")
            
        except Exception as e:
            logger.error(f"同步核心记忆到MD文件失败: {e}")

    def _sync_short_memory_to_md(self, avatar_name: str, user_id: str):
        """将短期记忆同步到MD文件（使用旧记忆系统的格式）"""
        try:
            newmemory_dir = self._get_avatar_newmemory_dir(avatar_name)
            md_path = os.path.join(newmemory_dir, "md", "short_memory.md")

            # 从数据库获取最近的对话记录
            db_path = self._get_short_memory_db_path(avatar_name)

            if not os.path.exists(db_path):
                return

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 查询最近10条对话记录
            cursor.execute('''
                SELECT user_message, bot_reply, timestamp FROM short_memory
                WHERE user_id = ?
                ORDER BY timestamp DESC
                LIMIT 10
            ''', (user_id,))

            records = cursor.fetchall()
            conn.close()

            if not records:
                return

            # 构建JSON格式的对话记录（与旧记忆系统格式兼容）
            conversations = []
            for user_message, bot_reply, timestamp in reversed(records):  # 反转以保持时间顺序
                # 如果timestamp是字符串，直接使用；如果是datetime对象，转换为字符串
                if isinstance(timestamp, str):
                    timestamp_str = timestamp
                else:
                    timestamp_str = timestamp.strftime("%Y-%m-%d %H:%M:%S")

                conversation = {
                    "timestamp": timestamp_str,
                    "user": user_message,
                    "bot": bot_reply
                }
                conversations.append(conversation)

            # 将对话记录保存为JSON格式到MD文件
            import json
            with open(md_path, 'w', encoding='utf-8') as f:
                json.dump(conversations, f, ensure_ascii=False, indent=2)

            logger.debug(f"短期记忆已同步到MD文件: {md_path}")

        except Exception as e:
            logger.error(f"同步短期记忆到MD文件失败: {e}")

    def _check_and_archive_memory(self, avatar_name: str, user_id: str):
        """检查并归档过期的短期记忆"""
        try:
            db_path = self._get_short_memory_db_path(avatar_name)

            if not os.path.exists(db_path):
                return

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 从配置获取归档天数
            from src.config import config
            archive_days = config.memory_and_story.memory_archive_days
            logger.debug(f"使用配置的归档天数: {archive_days} 天")

            # 获取指定天数前的记录
            archive_cutoff = datetime.now() - timedelta(days=archive_days)
            current_week = datetime.now().isocalendar()[1]
            
            cursor.execute('''
                SELECT * FROM short_memory
                WHERE user_id = ? AND timestamp < ?
            ''', (user_id, archive_cutoff))
            
            old_records = cursor.fetchall()
            
            if old_records:
                # 归档到MD文件
                self._archive_to_md(avatar_name, user_id, old_records)
                
                # 删除数据库中的旧记录
                cursor.execute('''
                    DELETE FROM short_memory
                    WHERE user_id = ? AND timestamp < ?
                ''', (user_id, archive_cutoff))
                
                conn.commit()
                logger.info(f"已归档 {len(old_records)} 条过期记忆记录（超过{archive_days}天）到 archived_memory 文件夹")
            
            conn.close()
            
        except Exception as e:
            logger.error(f"检查和归档记忆失败: {e}")
    
    def _archive_to_md(self, avatar_name: str, user_id: str, records: List[Tuple]):
        """将记录归档到MD文件"""
        try:
            # 获取归档天数配置
            from src.config import config
            archive_days = config.memory_and_story.memory_archive_days

            memory_dir = self._get_avatar_newmemory_dir(avatar_name)
            archive_dir = os.path.join(memory_dir, "archived_memory")
            os.makedirs(archive_dir, exist_ok=True)
            
            # 按周分组归档
            week_groups = {}
            for record in records:
                week_num = record[5]  # week_number字段
                if week_num not in week_groups:
                    week_groups[week_num] = []
                week_groups[week_num].append(record)
            
            # 为每周创建归档文件
            for week_num, week_records in week_groups.items():
                year = datetime.now().year
                archive_file = os.path.join(archive_dir, f"{year}-week{week_num}_archived.md")

                # 构建归档内容
                archive_content = f"# {year}年第{week_num}周对话记录（已归档）\n\n"
                archive_content += f"## 用户: {user_id}\n"
                archive_content += f"## 归档原因: 超过{archive_days}天的记忆自动归档\n\n"
                
                for record in week_records:
                    timestamp = record[4]  # timestamp字段
                    user_msg = record[2]   # user_message字段
                    bot_reply = record[3]  # bot_reply字段
                    
                    archive_content += f"### {timestamp}\n"
                    archive_content += f"**用户**: {user_msg}\n"
                    archive_content += f"**回复**: {bot_reply}\n\n"
                
                archive_content += f"---\n*归档时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n"
                
                with open(archive_file, 'w', encoding='utf-8') as f:
                    f.write(archive_content)
                
                logger.info(f"已创建归档文件: {archive_file}")
            
        except Exception as e:
            logger.error(f"归档记录到MD文件失败: {e}")

    def _initialize_database_tables(self):
        """初始化所有角色的数据库表"""
        try:
            avatars_dir = os.path.join(self.root_dir, "data", "avatars")

            if not os.path.exists(avatars_dir):
                return

            for avatar_name in os.listdir(avatars_dir):
                avatar_path = os.path.join(avatars_dir, avatar_name)
                if os.path.isdir(avatar_path):
                    self._initialize_avatar_tables(avatar_name)

            logger.info("数据库表初始化完成")

        except Exception as e:
            logger.error(f"初始化数据库表失败: {e}")

    def _initialize_avatar_tables(self, avatar_name: str):
        """初始化指定角色的数据库表"""
        try:
            # 初始化核心记忆表
            core_db_path = self._get_core_memory_db_path(avatar_name)
            self._create_memory_tables(core_db_path)

            # 初始化短期记忆表
            short_db_path = self._get_short_memory_db_path(avatar_name)
            self._create_memory_tables(short_db_path)

            logger.debug(f"角色 {avatar_name} 数据库表初始化完成")

        except Exception as e:
            logger.error(f"初始化角色 {avatar_name} 数据库表失败: {e}")

    def _create_memory_tables(self, db_path: str):
        """创建记忆数据库表"""
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 核心记忆表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS core_memory (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    content TEXT NOT NULL,
                    category TEXT DEFAULT 'general',
                    importance INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 短期记忆表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS short_memory (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    user_message TEXT NOT NULL,
                    bot_reply TEXT NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    week_number INTEGER NOT NULL
                )
            ''')

            # 文件同步状态表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sync_status (
                    file_path TEXT PRIMARY KEY,
                    file_hash TEXT NOT NULL,
                    last_sync TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"创建记忆数据库表失败: {e}")

    def initialize_avatar_memory(self, avatar_name: str, user_id: str):
        """初始化角色记忆文件和数据库"""
        try:
            # 确保目录结构存在
            memory_dir = self._get_avatar_newmemory_dir(avatar_name)

            # 初始化数据库表
            self._initialize_avatar_tables(avatar_name)

            # 创建默认MD文件（如果不存在）
            self._create_default_md_files(avatar_name, memory_dir, user_id)

            logger.info(f"角色 {avatar_name} 记忆系统初始化完成")

        except Exception as e:
            logger.error(f"初始化角色 {avatar_name} 记忆系统失败: {e}")

    def _create_default_md_files(self, avatar_name: str, memory_dir: str, user_id: str):
        """创建默认的MD文件"""
        try:
            from datetime import datetime

            md_dir = os.path.join(memory_dir, "md")

            # 创建默认核心记忆文件
            core_memory_path = os.path.join(md_dir, "core_memory.md")
            if not os.path.exists(core_memory_path):
                core_memory_content = f"""# {avatar_name} 核心记忆

## 用户: {user_id}

### 基本信息
- 用户ID: {user_id}
- 称呼偏好: 未设置
- 基本特征: 暂无记录

### 重要事件记录
- 暂无重要事件记录

### 用户偏好
- 对话风格: 未确定
- 兴趣爱好: 暂无记录
- 特殊需求: 暂无记录

### 关系发展
- 初次见面时间: 未记录
- 关系状态: 新用户
- 互动频率: 暂无数据

---
*最后更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
*记录状态: 初始化*
"""
                with open(core_memory_path, 'w', encoding='utf-8') as f:
                    f.write(core_memory_content)

            # 创建默认短期记忆文件（只在文件不存在时创建，避免覆盖现有记忆）
            short_memory_path = os.path.join(md_dir, "short_memory.md")
            if not os.path.exists(short_memory_path):
                # 创建空的JSON数组，与旧记忆系统格式兼容
                with open(short_memory_path, 'w', encoding='utf-8') as f:
                    import json
                    json.dump([], f, ensure_ascii=False, indent=2)
                logger.info(f"创建默认短期记忆文件: {short_memory_path}")
            else:
                logger.debug(f"短期记忆文件已存在，跳过创建: {short_memory_path}")

            logger.debug(f"为角色 {avatar_name} 创建默认MD文件成功")

        except Exception as e:
            logger.error(f"创建默认MD文件失败: {e}")
