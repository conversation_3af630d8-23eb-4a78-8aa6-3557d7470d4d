#!/usr/bin/env python3
"""
创建离线依赖包脚本
用于在有网络的电脑上下载所有依赖，然后打包给没有网络或网络受限的电脑使用
"""

import os
import sys
import subprocess
import shutil
import zipfile
from pathlib import Path

def create_offline_deps():
    """创建离线依赖包"""
    print("🚀 开始创建离线依赖包...")
    
    # 创建临时目录
    temp_dir = Path("temp_deps")
    if temp_dir.exists():
        shutil.rmtree(temp_dir)
    temp_dir.mkdir()
    
    # 下载所有依赖到临时目录
    print("📦 正在下载依赖包...")
    try:
        # 使用pip download下载所有依赖
        subprocess.run([
            sys.executable, "-m", "pip", "download",
            "-r", "requirements.txt",
            "-d", str(temp_dir),
            "--no-deps"  # 不下载依赖的依赖，避免重复
        ], check=True)
        
        # 再下载依赖的依赖
        subprocess.run([
            sys.executable, "-m", "pip", "download",
            "-r", "requirements.txt",
            "-d", str(temp_dir)
        ], check=True)
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 下载依赖失败: {e}")
        return False
    
    # 手动下载wxauto改版
    print("📥 正在下载wxauto改版...")
    try:
        # 克隆wxauto改版仓库
        subprocess.run([
            "git", "clone", 
            "https://github.com/cluic/wxauto.git",
            str(temp_dir / "wxauto_modified")
        ], check=True)
    except subprocess.CalledProcessError:
        print("⚠️ Git下载失败，将跳过wxauto改版")
    
    # 创建安装脚本
    install_script = temp_dir / "install_offline.bat"
    with open(install_script, 'w', encoding='utf-8') as f:
        f.write("""@echo off
echo 正在安装离线依赖包...

:: 安装所有wheel文件
for %%f in (*.whl) do (
    echo 安装: %%f
    python -m pip install "%%f" --no-index --find-links .
)

:: 安装所有tar.gz文件
for %%f in (*.tar.gz) do (
    echo 安装: %%f
    python -m pip install "%%f" --no-index --find-links .
)

:: 如果存在wxauto改版，安装它
if exist "wxauto_modified" (
    echo 安装wxauto改版...
    cd wxauto_modified
    python -m pip install .
    cd ..
)

echo 离线依赖安装完成！
pause
""")
    
    # 创建Python安装脚本
    py_install_script = temp_dir / "install_offline.py"
    with open(py_install_script, 'w', encoding='utf-8') as f:
        f.write("""#!/usr/bin/env python3
import os
import sys
import subprocess
import glob

def install_offline_deps():
    print("正在安装离线依赖包...")
    
    # 获取当前目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 安装所有wheel和tar.gz文件
    for pattern in ["*.whl", "*.tar.gz"]:
        files = glob.glob(os.path.join(current_dir, pattern))
        for file in files:
            print(f"安装: {os.path.basename(file)}")
            try:
                subprocess.run([
                    sys.executable, "-m", "pip", "install", 
                    file, "--no-index", "--find-links", current_dir
                ], check=True)
            except subprocess.CalledProcessError as e:
                print(f"安装失败: {e}")
    
    # 安装wxauto改版
    wxauto_dir = os.path.join(current_dir, "wxauto_modified")
    if os.path.exists(wxauto_dir):
        print("安装wxauto改版...")
        try:
            subprocess.run([
                sys.executable, "-m", "pip", "install", wxauto_dir
            ], check=True)
        except subprocess.CalledProcessError as e:
            print(f"wxauto改版安装失败: {e}")
    
    print("离线依赖安装完成！")

if __name__ == "__main__":
    install_offline_deps()
""")
    
    # 打包成zip文件
    print("📦 正在打包...")
    zip_path = "offline_deps.zip"
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in temp_dir.rglob('*'):
            if file_path.is_file():
                arcname = file_path.relative_to(temp_dir)
                zipf.write(file_path, arcname)
    
    # 清理临时目录
    shutil.rmtree(temp_dir)
    
    print(f"✅ 离线依赖包已创建: {zip_path}")
    print("📋 使用方法:")
    print("1. 将 offline_deps.zip 复制到目标电脑")
    print("2. 解压到项目目录")
    print("3. 运行 install_offline.bat 或 python install_offline.py")
    
    return True

if __name__ == "__main__":
    create_offline_deps()
