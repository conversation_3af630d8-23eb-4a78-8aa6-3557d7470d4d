#!/usr/bin/env python3
"""
创建wxauto改版离线包
从GitHub下载wxauto改版并打包成可离线安装的格式
"""

import os
import sys
import subprocess
import shutil
import zipfile
import tempfile
from pathlib import Path
import urllib.request
import json

def download_wxauto_modified():
    """下载并打包wxauto改版"""
    print("🚀 开始创建wxauto改版离线包...")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        try:
            # 方法1: 尝试使用git克隆
            print("📥 尝试使用Git下载...")
            subprocess.run([
                "git", "clone", 
                "https://github.com/cluic/wxauto.git",
                str(temp_path / "wxauto")
            ], check=True)
            
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("⚠️ Git不可用，尝试下载ZIP包...")
            
            # 方法2: 下载ZIP包
            try:
                zip_url = "https://github.com/cluic/wxauto/archive/refs/heads/main.zip"
                zip_path = temp_path / "wxauto.zip"
                
                print(f"📥 正在下载: {zip_url}")
                urllib.request.urlretrieve(zip_url, zip_path)
                
                # 解压ZIP
                with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                    zip_ref.extractall(temp_path)
                
                # 重命名解压后的文件夹
                extracted_dir = temp_path / "wxauto-main"
                if extracted_dir.exists():
                    extracted_dir.rename(temp_path / "wxauto")
                
            except Exception as e:
                print(f"❌ 下载失败: {e}")
                return False
        
        wxauto_dir = temp_path / "wxauto"
        if not wxauto_dir.exists():
            print("❌ wxauto目录不存在")
            return False
        
        # 创建安装包目录
        package_dir = Path("wxauto_modified_package")
        if package_dir.exists():
            shutil.rmtree(package_dir)
        package_dir.mkdir()
        
        # 复制wxauto源码
        shutil.copytree(wxauto_dir, package_dir / "wxauto_source")
        
        # 创建安装脚本
        install_script = package_dir / "install_wxauto_modified.py"
        with open(install_script, 'w', encoding='utf-8') as f:
            f.write("""#!/usr/bin/env python3
import os
import sys
import subprocess
import shutil
from pathlib import Path

def install_wxauto_modified():
    print("🔧 正在安装wxauto改版...")
    
    # 获取当前脚本目录
    script_dir = Path(__file__).parent
    wxauto_source = script_dir / "wxauto_source"
    
    if not wxauto_source.exists():
        print("❌ 找不到wxauto源码目录")
        return False
    
    try:
        # 先卸载原版wxauto（如果存在）
        print("🗑️ 卸载原版wxauto...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "uninstall", "wxauto", "-y"], 
                         check=False, capture_output=True)
        except:
            pass
        
        # 安装改版wxauto
        print("📦 安装wxauto改版...")
        os.chdir(wxauto_source)
        result = subprocess.run([sys.executable, "-m", "pip", "install", "."], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ wxauto改版安装成功！")
            print("🎉 现在支持VoiceCall功能了！")
            return True
        else:
            print(f"❌ 安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 安装过程中出错: {e}")
        return False

if __name__ == "__main__":
    success = install_wxauto_modified()
    if not success:
        print("\\n💡 如果安装失败，请尝试:")
        print("1. 确保Python和pip正常工作")
        print("2. 以管理员权限运行")
        print("3. 手动进入wxauto_source目录执行: pip install .")
    
    input("\\n按回车键退出...")
""")
        
        # 创建批处理安装脚本
        bat_script = package_dir / "install_wxauto_modified.bat"
        with open(bat_script, 'w', encoding='utf-8') as f:
            f.write("""@echo off
echo 正在安装wxauto改版...

python install_wxauto_modified.py

pause
""")
        
        # 创建说明文件
        readme = package_dir / "README.md"
        with open(readme, 'w', encoding='utf-8') as f:
            f.write("""# wxauto改版离线安装包

这个包包含了支持VoiceCall功能的wxauto改版。

## 安装方法

### 方法1: 使用Python脚本
```bash
python install_wxauto_modified.py
```

### 方法2: 使用批处理文件
```bash
install_wxauto_modified.bat
```

### 方法3: 手动安装
```bash
cd wxauto_source
pip install .
```

## 功能特性

- ✅ 支持VoiceCall语音通话功能
- ✅ 兼容原版wxauto的所有功能
- ✅ 来自GitHub PR #271

## 注意事项

- 安装前会自动卸载原版wxauto
- 需要Python 3.7+环境
- 建议在虚拟环境中安装
""")
        
        # 打包成ZIP
        print("📦 正在打包...")
        zip_path = "wxauto_modified_package.zip"
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in package_dir.rglob('*'):
                if file_path.is_file():
                    arcname = file_path.relative_to(package_dir.parent)
                    zipf.write(file_path, arcname)
        
        # 清理临时目录
        shutil.rmtree(package_dir)
        
        print(f"✅ wxauto改版离线包已创建: {zip_path}")
        print("📋 使用方法:")
        print("1. 将 wxauto_modified_package.zip 复制到目标电脑")
        print("2. 解压到任意目录")
        print("3. 运行 install_wxauto_modified.bat 或 python install_wxauto_modified.py")
        print("4. 安装完成后就可以使用VoiceCall功能了")
        
        return True

if __name__ == "__main__":
    download_wxauto_modified()
