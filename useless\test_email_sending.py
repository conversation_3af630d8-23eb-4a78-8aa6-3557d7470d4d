#!/usr/bin/env python3
"""
测试微信截图邮件发送功能
"""

import os
import sys
import time
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.wechat_reconnect.email_sender import EmailSender

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_email_config():
    """测试邮件配置"""
    print("=== 测试邮件发送功能 ===")
    print("请输入邮件配置信息：")
    
    # 获取用户输入的邮件配置
    smtp_server = input("SMTP服务器 (例如: smtp.qq.com): ").strip()
    if not smtp_server:
        smtp_server = "smtp.qq.com"
    
    smtp_port = input("SMTP端口 (默认: 587): ").strip()
    if not smtp_port:
        smtp_port = 587
    else:
        smtp_port = int(smtp_port)
    
    sender_email = input("发送邮箱: ").strip()
    if not sender_email:
        print("❌ 发送邮箱不能为空")
        return False
    
    sender_password = input("邮箱密码/授权码: ").strip()
    if not sender_password:
        print("❌ 邮箱密码不能为空")
        return False
    
    recipient_email = input("接收邮箱 (默认同发送邮箱): ").strip()
    if not recipient_email:
        recipient_email = sender_email
    
    return {
        'smtp_server': smtp_server,
        'smtp_port': smtp_port,
        'sender_email': sender_email,
        'sender_password': sender_password,
        'recipient_email': recipient_email
    }

def test_email_sending():
    """测试邮件发送"""
    try:
        # 获取邮件配置
        config = test_email_config()
        if not config:
            return
        
        print(f"\n使用配置:")
        print(f"SMTP服务器: {config['smtp_server']}:{config['smtp_port']}")
        print(f"发送邮箱: {config['sender_email']}")
        print(f"接收邮箱: {config['recipient_email']}")
        
        # 创建邮件发送器
        email_sender = EmailSender(
            smtp_server=config['smtp_server'],
            smtp_port=config['smtp_port'],
            sender_email=config['sender_email'],
            sender_password=config['sender_password'],
            recipient_email=config['recipient_email']
        )
        
        # 测试连接
        print("\n正在测试邮箱连接...")
        if not email_sender.test_connection():
            print("❌ 邮箱连接测试失败")
            return
        
        print("✅ 邮箱连接测试成功")
        
        # 查找截图文件
        screenshot_files = []
        cache_dir = "data/cache"
        if os.path.exists(cache_dir):
            for file in os.listdir(cache_dir):
                if file.startswith("wechat_screenshot_") or file.startswith("test_screenshot_"):
                    screenshot_files.append(os.path.join(cache_dir, file))
        
        if not screenshot_files:
            print("❌ 没有找到截图文件，请先运行 test_wechat_screenshot.py")
            return
        
        # 使用最新的截图文件
        screenshot_file = max(screenshot_files, key=os.path.getmtime)
        print(f"\n使用截图文件: {screenshot_file}")
        print(f"文件大小: {os.path.getsize(screenshot_file)} 字节")
        
        # 发送邮件
        print("\n正在发送微信截图邮件...")
        success = email_sender.send_qrcode(screenshot_file, retry_count=0)
        
        if success:
            print("✅ 邮件发送成功！")
            print(f"请检查邮箱: {config['recipient_email']}")
        else:
            print("❌ 邮件发送失败")
            
    except KeyboardInterrupt:
        print("\n用户取消操作")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_complete_flow():
    """测试完整的重连流程"""
    print("\n=== 测试完整重连流程 ===")
    
    try:
        # 获取邮件配置
        config = test_email_config()
        if not config:
            return
        
        # 创建重连管理器配置
        class TestReconnectConfig:
            def __init__(self, email_config):
                self.enable_auto_reconnect = True
                self.check_interval = 60
                self.max_retry_attempts = 3
                self.qrcode_retry_interval = 120
                self.email_enabled = True
                self.smtp_server = email_config['smtp_server']
                self.smtp_port = email_config['smtp_port']
                self.sender_email = email_config['sender_email']
                self.sender_password = email_config['sender_password']
                self.recipient_email = email_config['recipient_email']
        
        class TestConfig:
            def __init__(self, email_config):
                self.wechat_reconnect = TestReconnectConfig(email_config)
        
        from src.wechat_reconnect.reconnect_manager import WeChatReconnectManager
        
        # 创建重连管理器
        manager = WeChatReconnectManager(
            wx_instance=None,
            config=TestConfig(config)
        )
        
        print("✅ 重连管理器创建成功")
        print("✅ 邮件配置验证成功")
        
        # 测试截图和邮件发送
        print("\n正在测试截图和邮件发送...")
        manager._handle_qrcode_login()
        
        print("✅ 完整流程测试完成")
        
    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("微信截图邮件发送测试")
    print("=" * 50)
    
    while True:
        print("\n请选择测试项目:")
        print("1. 测试邮件发送功能")
        print("2. 测试完整重连流程")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == "1":
            test_email_sending()
        elif choice == "2":
            test_complete_flow()
        elif choice == "3":
            print("退出测试")
            break
        else:
            print("无效选择，请重新输入")
