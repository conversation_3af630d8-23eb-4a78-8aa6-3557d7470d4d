#!/usr/bin/env python3
"""
微信重连模块测试脚本
用于测试各项功能是否正常工作
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_config_loading():
    """测试配置加载"""
    print("🔧 测试配置加载...")
    
    try:
        from src.config import config
        
        # 检查微信重连配置是否存在
        if hasattr(config, 'wechat_reconnect'):
            print("✅ 微信重连配置加载成功")
            
            # 显示当前配置
            reconnect_config = config.wechat_reconnect
            print(f"   - 自动重连启用: {reconnect_config.enable_auto_reconnect}")
            print(f"   - 检查间隔: {reconnect_config.check_interval}秒")
            print(f"   - 最大重试次数: {reconnect_config.max_retry_attempts}")
            print(f"   - 邮件功能: {reconnect_config.email_enabled}")
            
            return True
        else:
            print("❌ 微信重连配置不存在")
            return False
            
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False

def test_email_sender():
    """测试邮件发送器"""
    print("\n📧 测试邮件发送器...")
    
    try:
        from src.wechat_reconnect.email_sender import EmailSender, get_smtp_config
        
        # 测试SMTP配置获取
        test_emails = [
            "<EMAIL>",
            "<EMAIL>", 
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        print("   SMTP配置测试:")
        for email in test_emails:
            smtp_server, smtp_port = get_smtp_config(email)
            print(f"   - {email}: {smtp_server}:{smtp_port}")
        
        print("✅ 邮件发送器模块加载成功")
        return True
        
    except Exception as e:
        print(f"❌ 邮件发送器测试失败: {e}")
        return False

def test_reconnect_manager():
    """测试重连管理器"""
    print("\n🔄 测试重连管理器...")
    
    try:
        from src.wechat_reconnect.reconnect_manager import WeChatReconnectManager
        from src.config import config
        
        # 创建模拟的微信实例
        class MockWeChat:
            def IsOnline(self):
                return True
            
            def GetSessionList(self):
                return ["测试会话"]
        
        mock_wx = MockWeChat()
        
        # 创建重连管理器
        manager = WeChatReconnectManager(
            wx_instance=mock_wx,
            config=config
        )
        
        # 测试状态获取
        status = manager.get_status()
        print(f"   - 管理器状态: {status}")
        
        print("✅ 重连管理器创建成功")
        return True
        
    except Exception as e:
        print(f"❌ 重连管理器测试失败: {e}")
        return False

def test_standalone_monitor():
    """测试独立监控器"""
    print("\n🖥️ 测试独立监控器...")
    
    try:
        from src.wechat_reconnect.standalone_monitor import StandaloneWeChatMonitor
        
        # 创建监控器实例
        monitor = StandaloneWeChatMonitor()
        
        print("✅ 独立监控器创建成功")
        return True
        
    except Exception as e:
        print(f"❌ 独立监控器测试失败: {e}")
        return False

def test_integration():
    """测试集成模块"""
    print("\n🔗 测试集成模块...")
    
    try:
        from src.wechat_reconnect.integration import get_reconnect_status
        
        # 获取状态（应该返回未初始化的状态）
        status = get_reconnect_status()
        print(f"   - 集成状态: {status}")
        
        print("✅ 集成模块加载成功")
        return True
        
    except Exception as e:
        print(f"❌ 集成模块测试失败: {e}")
        return False

def test_config_wizard():
    """测试配置向导"""
    print("\n🧙 测试配置向导...")
    
    try:
        from src.wechat_reconnect.config_wizard import WeChatReconnectConfigWizard
        
        # 创建配置向导实例
        wizard = WeChatReconnectConfigWizard()
        
        print("✅ 配置向导创建成功")
        return True
        
    except Exception as e:
        print(f"❌ 配置向导测试失败: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    print("\n📁 测试文件结构...")
    
    required_files = [
        "src/wechat_reconnect/__init__.py",
        "src/wechat_reconnect/email_sender.py",
        "src/wechat_reconnect/reconnect_manager.py",
        "src/wechat_reconnect/integration.py",
        "src/wechat_reconnect/standalone_monitor.py",
        "src/wechat_reconnect/config_wizard.py",
        "src/wechat_reconnect/README.md",
        "start_wechat_monitor.bat",
        "start_wechat_monitor.sh"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少以下文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    else:
        print("✅ 所有必需文件都存在")
        return True

def main():
    """主测试函数"""
    print("=" * 60)
    print("           微信重连模块测试")
    print("=" * 60)
    
    tests = [
        ("文件结构", test_file_structure),
        ("配置加载", test_config_loading),
        ("邮件发送器", test_email_sender),
        ("重连管理器", test_reconnect_manager),
        ("独立监控器", test_standalone_monitor),
        ("集成模块", test_integration),
        ("配置向导", test_config_wizard),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print("                测试结果")
    print("=" * 60)
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    print(f"📊 总计: {passed + failed}")
    
    if failed == 0:
        print("\n🎉 所有测试通过！微信重连模块已准备就绪。")
        print("\n下一步:")
        print("1. 运行配置向导: python src/wechat_reconnect/config_wizard.py")
        print("2. 启动监控: start_wechat_monitor.bat (Windows) 或 ./start_wechat_monitor.sh (Linux/Mac)")
    else:
        print(f"\n⚠️ 有 {failed} 个测试失败，请检查相关模块。")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
