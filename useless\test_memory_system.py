#!/usr/bin/env python3
"""
记忆系统测试脚本
测试新的记忆管理器和剧情系统功能
"""

import os
import sys
import logging

# 添加项目根目录到Python路径
root_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(root_dir)
sys.path.append(os.path.join(root_dir, 'src'))

# 禁用字节码缓存
sys.dont_write_bytecode = True

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_memory_manager():
    """测试记忆管理器"""
    try:
        from modules.memory_manager import MemoryManager
        from src.config import config
        
        print("=" * 50)
        print("测试记忆管理器")
        print("=" * 50)
        
        # 创建记忆管理器实例
        memory_manager = MemoryManager(
            root_dir=root_dir,
            api_key="test_key",
            base_url="https://api.test.com/v1",
            model="test-model",
            max_token=2000,
            temperature=1.0,
            max_groups=10
        )
        
        print(f"✓ 记忆管理器创建成功")
        
        # 获取系统信息
        info = memory_manager.get_memory_system_info()
        print(f"✓ 记忆系统信息:")
        for key, value in info.items():
            print(f"  - {key}: {value}")
        
        # 测试记忆初始化
        test_avatar = "ATRI"
        test_user = "test_user"
        
        print(f"\n测试角色记忆初始化: {test_avatar}")
        memory_manager.initialize_avatar_memory(test_avatar, test_user)
        print(f"✓ 角色记忆初始化完成")
        
        # 测试添加对话
        print(f"\n测试添加对话")
        memory_manager.add_conversation(
            avatar_name=test_avatar,
            user_message="你好，我是测试用户",
            bot_reply="你好！很高兴认识你",
            user_id=test_user
        )
        print(f"✓ 对话添加成功")
        
        # 测试获取上下文
        print(f"\n测试获取对话上下文")
        context = memory_manager.get_recent_context(test_avatar, test_user)
        print(f"✓ 获取到 {len(context)} 条上下文记录")
        
        # 测试获取核心记忆
        print(f"\n测试获取核心记忆")
        core_memory = memory_manager.get_core_memory(test_avatar, test_user)
        print(f"✓ 核心记忆长度: {len(core_memory)} 字符")
        
        # 测试剧情上下文
        print(f"\n测试获取剧情上下文")
        story_context = memory_manager.get_story_context(test_avatar, "告诉我你的故事")
        print(f"✓ 剧情上下文长度: {len(story_context)} 字符")
        
        print(f"\n✓ 记忆管理器测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 记忆管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sync_service():
    """测试同步服务"""
    try:
        from modules.newmemory.sync_initializer import SyncInitializer
        
        print("\n" + "=" * 50)
        print("测试同步服务")
        print("=" * 50)
        
        # 创建同步初始化器
        sync_initializer = SyncInitializer(root_dir)
        print(f"✓ 同步初始化器创建成功")
        
        # 测试创建默认结构
        test_avatar = "ATRI"
        print(f"\n测试创建默认结构: {test_avatar}")
        success = sync_initializer.create_default_structure(test_avatar)
        if success:
            print(f"✓ 默认结构创建成功")
        else:
            print(f"✗ 默认结构创建失败")
        
        # 测试同步指定角色
        print(f"\n测试同步指定角色: {test_avatar}")
        success = sync_initializer.sync_specific_avatar(test_avatar)
        if success:
            print(f"✓ 角色同步成功")
        else:
            print(f"✗ 角色同步失败")
        
        print(f"\n✓ 同步服务测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 同步服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_story_service():
    """测试剧情服务"""
    try:
        from modules.story.story_service import StoryService
        from modules.story.story_classifier import StoryClassifier
        
        print("\n" + "=" * 50)
        print("测试剧情服务")
        print("=" * 50)
        
        # 创建剧情服务
        story_service = StoryService(
            root_dir=root_dir,
            api_key="test_key",
            base_url="https://api.test.com/v1",
            model="test-model",
            max_token=2000,
            temperature=1.0
        )
        print(f"✓ 剧情服务创建成功")
        
        # 创建剧情分类器
        story_classifier = StoryClassifier(
            root_dir=root_dir,
            api_key="test_key",
            base_url="https://api.test.com/v1",
            model="test-model",
            max_token=2000,
            temperature=1.0
        )
        print(f"✓ 剧情分类器创建成功")
        
        test_avatar = "ATRI"
        
        # 测试获取所有剧情内容
        print(f"\n测试获取所有剧情内容: {test_avatar}")
        all_content = story_service.get_all_story_content(test_avatar)
        print(f"✓ 获取到剧情内容类别: {list(all_content.keys())}")
        
        # 测试查询相关剧情
        print(f"\n测试查询相关剧情")
        relevant_story = story_service.query_relevant_story(test_avatar, "你好")
        print(f"✓ 查询到相关剧情类别: {list(relevant_story.keys())}")
        
        # 测试格式化剧情上下文
        print(f"\n测试格式化剧情上下文")
        formatted_context = story_service.format_story_context(relevant_story)
        print(f"✓ 格式化上下文长度: {len(formatted_context)} 字符")
        
        print(f"\n✓ 剧情服务测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 剧情服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_integration():
    """测试配置集成"""
    try:
        from src.config import config
        
        print("\n" + "=" * 50)
        print("测试配置集成")
        print("=" * 50)
        
        # 检查新的配置项
        print(f"记忆系统类型: {config.memory_and_story.memory_system_type}")
        print(f"启用剧情数据库: {config.memory_and_story.enable_story_database}")
        print(f"自动记忆总结: {config.memory_and_story.auto_memory_summary}")
        print(f"记忆归档天数: {config.memory_and_story.memory_archive_days}")
        print(f"剧情自动分类: {config.memory_and_story.story_auto_classify}")
        print(f"剧情查询启用: {config.memory_and_story.story_query_enabled}")
        print(f"最大剧情结果数: {config.memory_and_story.max_story_results}")
        
        print(f"\n✓ 配置集成测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 配置集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    setup_logging()
    
    print("开始记忆系统测试")
    print("=" * 60)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("配置集成", test_config_integration()))
    test_results.append(("同步服务", test_sync_service()))
    test_results.append(("剧情服务", test_story_service()))
    test_results.append(("记忆管理器", test_memory_manager()))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！新记忆系统可以正常使用")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关模块")
        return 1

if __name__ == "__main__":
    sys.exit(main())
