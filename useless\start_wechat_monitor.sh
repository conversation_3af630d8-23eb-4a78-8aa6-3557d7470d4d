#!/bin/bash

# 微信重连监控器启动脚本

echo "========================================"
echo "           微信重连监控器"
echo "========================================"
echo

show_menu() {
    echo "请选择操作："
    echo "1. 启动微信重连监控"
    echo "2. 测试邮件配置"
    echo "3. 强制重连微信"
    echo "4. 后台运行监控"
    echo "5. 停止后台监控"
    echo "6. 查看监控状态"
    echo "7. 退出"
    echo
}

start_monitor() {
    echo
    echo "正在启动微信重连监控..."
    echo "按 Ctrl+C 可以停止监控"
    echo
    python3 src/wechat_reconnect/standalone_monitor.py
    echo
    echo "监控已停止"
    read -p "按回车键继续..."
}

test_email() {
    echo
    echo "正在测试邮件配置..."
    python3 src/wechat_reconnect/standalone_monitor.py --test-email
    echo
    read -p "按回车键继续..."
}

force_reconnect() {
    echo
    echo "正在执行强制重连..."
    python3 src/wechat_reconnect/standalone_monitor.py --force-reconnect
    echo
    read -p "按回车键继续..."
}

start_daemon() {
    echo
    echo "正在启动后台监控..."
    
    # 检查是否已经在运行
    if pgrep -f "standalone_monitor.py" > /dev/null; then
        echo "监控进程已在运行"
        return
    fi
    
    # 启动后台进程
    nohup python3 src/wechat_reconnect/standalone_monitor.py --daemon > logs/wechat_monitor.log 2>&1 &
    
    if [ $? -eq 0 ]; then
        echo "后台监控已启动"
        echo "日志文件: logs/wechat_monitor.log"
    else
        echo "启动后台监控失败"
    fi
    
    read -p "按回车键继续..."
}

stop_daemon() {
    echo
    echo "正在停止后台监控..."
    
    # 查找并终止进程
    pids=$(pgrep -f "standalone_monitor.py")
    
    if [ -z "$pids" ]; then
        echo "没有找到运行中的监控进程"
    else
        for pid in $pids; do
            kill $pid
            echo "已停止进程 $pid"
        done
        
        # 等待进程完全停止
        sleep 2
        
        # 检查是否还有残留进程
        remaining=$(pgrep -f "standalone_monitor.py")
        if [ -n "$remaining" ]; then
            echo "强制终止残留进程..."
            pkill -9 -f "standalone_monitor.py"
        fi
        
        echo "后台监控已停止"
    fi
    
    read -p "按回车键继续..."
}

check_status() {
    echo
    echo "检查监控状态..."
    
    pids=$(pgrep -f "standalone_monitor.py")
    
    if [ -z "$pids" ]; then
        echo "监控进程未运行"
    else
        echo "监控进程正在运行:"
        for pid in $pids; do
            echo "  PID: $pid"
            ps -p $pid -o pid,ppid,cmd --no-headers
        done
        
        # 显示最近的日志
        if [ -f "logs/wechat_monitor.log" ]; then
            echo
            echo "最近的日志 (最后10行):"
            tail -n 10 logs/wechat_monitor.log
        fi
    fi
    
    echo
    read -p "按回车键继续..."
}

# 主循环
while true; do
    clear
    echo "========================================"
    echo "           微信重连监控器"
    echo "========================================"
    echo
    
    show_menu
    read -p "请输入选择 (1-7): " choice
    
    case $choice in
        1)
            start_monitor
            ;;
        2)
            test_email
            ;;
        3)
            force_reconnect
            ;;
        4)
            start_daemon
            ;;
        5)
            stop_daemon
            ;;
        6)
            check_status
            ;;
        7)
            echo "再见！"
            exit 0
            ;;
        *)
            echo "无效选择，请重新输入"
            sleep 1
            ;;
    esac
done
