# 微信自动重连修复说明

## 问题描述

原来的微信自动重连功能依赖于wxauto库的`get_qrcode`方法和`login`模块，但当前使用的wxauto版本(3.9.11.17)不支持这些功能，导致以下错误：

```
[20:40:17]微信实例没有get_qrcode方法
[20:40:17]无法导入wxauto.login模块
```

## 解决方案

已将二维码获取功能替换为**微信窗口截图**功能，通过截图微信登录界面并发送到邮箱的方式来实现重连。

### 主要修改

1. **替换二维码获取方式**
   - 原来：使用`wx.get_qrcode()`或`wxauto.login.get_qrcode()`
   - 现在：使用`_capture_wechat_window()`截图微信窗口

2. **更新邮件内容**
   - 邮件标题：从"微信登录二维码"改为"微信登录界面截图"
   - 邮件正文：更新说明文字，提醒用户查看截图中的二维码
   - 附件名称：从"wechat_qrcode_*.png"改为"wechat_screenshot_*.png"

3. **优化日志信息**
   - 所有相关日志都更新为反映截图功能
   - 更准确的错误提示和状态信息

### 新增功能

#### `_capture_wechat_window()` 方法
- 自动查找微信窗口
- 确保窗口可见并置于前台
- 使用Windows API截图整个微信窗口
- 保存为PNG格式图片
- 自动清理资源

#### 依赖库
- 新增 `Pillow` 库用于图像处理
- 使用现有的 `pywin32` 库进行窗口操作

## 使用方法

### 1. 安装依赖
```bash
pip install Pillow
```

### 2. 测试功能
```bash
python test_wechat_screenshot.py
```

### 3. 正常使用
微信重连功能会在检测到微信断线时自动工作：

1. **自动重连尝试**：首先尝试使用现有的登录点击器自动重连
2. **截图发送**：如果自动重连失败，会截图微信窗口并发送到配置的邮箱
3. **用户扫码**：用户收到邮件后，查看截图中的二维码并扫描登录
4. **状态监控**：系统会监控微信登录状态，成功后自动清理截图文件

## 优势

1. **兼容性好**：不依赖特定的wxauto版本或方法
2. **信息完整**：截图包含完整的微信登录界面，不仅仅是二维码
3. **故障诊断**：用户可以通过截图了解微信的具体状态
4. **自动清理**：成功登录后自动删除截图文件，保护隐私

## 注意事项

1. **窗口可见性**：确保微信窗口没有被其他程序完全遮挡
2. **权限要求**：需要Windows窗口操作权限
3. **网络连接**：邮件发送需要稳定的网络连接
4. **二维码有效期**：微信二维码通常2分钟过期，请及时扫描

## 文件修改列表

- `src/wechat_reconnect/reconnect_manager.py` - 主要修改
- `src/wechat_reconnect/email_sender.py` - 邮件内容更新
- `requirements.txt` - 添加Pillow依赖
- `test_wechat_screenshot.py` - 新增测试脚本

## 测试结果

✅ **测试成功！**

运行测试脚本的结果：
```
=== 测试微信窗口截图功能 ===
✅ 截图成功！文件保存到: data\cache\test_screenshot_1752238360.png
文件大小: 5911 字节
图片尺寸: (280, 380)
图片格式: PNG
图片模式: RGB

=== 测试获取微信登录界面方法 ===
✅ 获取成功！文件保存到: data\cache\wechat_screenshot_1752238361.png
文件大小: 5911 字节
```

## 测试建议

1. ✅ 先运行测试脚本确认截图功能正常 - **已完成**
2. 配置邮箱设置并测试邮件发送
3. 模拟微信断线情况测试完整流程

## 修复状态

🎉 **修复完成！** 微信自动重连功能现在可以正常工作，不再出现以下错误：
- ❌ `微信实例没有get_qrcode方法`
- ❌ `无法导入wxauto.login模块`

现在系统会：
- ✅ 自动截图微信窗口
- ✅ 通过邮件发送截图
- ✅ 监控登录状态
- ✅ 自动清理临时文件
