#!/usr/bin/env python3
"""
微信重连模块使用示例
演示如何使用微信重连功能
"""

import time
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    try:
        from src.config import config
        from src.wechat_reconnect.reconnect_manager import WeChatReconnectManager
        
        # 模拟微信实例
        class MockWeChat:
            def __init__(self):
                self.online = True
            
            def IsOnline(self):
                return self.online
            
            def GetSessionList(self):
                return ["测试会话"] if self.online else None
        
        # 创建模拟微信实例
        wx = MockWeChat()
        
        # 重连成功回调
        def on_reconnect_success():
            print("✅ 微信重连成功！")
        
        # 创建重连管理器
        manager = WeChatReconnectManager(
            wx_instance=wx,
            config=config,
            on_reconnect_success=on_reconnect_success
        )
        
        # 获取状态
        status = manager.get_status()
        print(f"初始状态: {status}")
        
        # 测试邮件配置（如果已配置）
        if config.wechat_reconnect.email_enabled:
            print("测试邮件配置...")
            email_ok = manager.test_email_config()
            print(f"邮件配置测试: {'✅ 成功' if email_ok else '❌ 失败'}")
        
        print("✅ 基本使用示例完成")
        
    except Exception as e:
        print(f"❌ 基本使用示例失败: {e}")

def example_standalone_monitor():
    """独立监控器示例"""
    print("\n=== 独立监控器示例 ===")
    
    try:
        from src.wechat_reconnect.standalone_monitor import StandaloneWeChatMonitor
        
        # 创建监控器
        monitor = StandaloneWeChatMonitor()
        
        print("监控器创建成功")
        print("注意：实际使用时需要确保微信已登录")
        
        # 显示可用的操作
        print("\n可用操作:")
        print("- monitor.test_email(): 测试邮件配置")
        print("- monitor.force_reconnect(): 强制重连")
        print("- monitor.run_forever(): 持续监控")
        
        print("✅ 独立监控器示例完成")
        
    except Exception as e:
        print(f"❌ 独立监控器示例失败: {e}")

def example_email_configuration():
    """邮件配置示例"""
    print("\n=== 邮件配置示例 ===")
    
    try:
        from src.wechat_reconnect.email_sender import get_smtp_config, EmailSender
        
        # 演示不同邮箱的SMTP配置
        test_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        print("不同邮箱提供商的SMTP配置:")
        for email in test_emails:
            smtp_server, smtp_port = get_smtp_config(email)
            print(f"  {email:20} -> {smtp_server}:{smtp_port}")
        
        print("\n邮件发送器配置示例:")
        print("""
# 创建邮件发送器
sender = EmailSender(
    smtp_server="smtp.qq.com",
    smtp_port=587,
    sender_email="<EMAIL>",
    sender_password="your_auth_code",  # QQ邮箱授权码
    recipient_email="<EMAIL>"
)

# 发送二维码
success = sender.send_qrcode("/path/to/qrcode.png")
        """)
        
        print("✅ 邮件配置示例完成")
        
    except Exception as e:
        print(f"❌ 邮件配置示例失败: {e}")

def example_integration():
    """集成使用示例"""
    print("\n=== 集成使用示例 ===")
    
    try:
        from src.wechat_reconnect.integration import (
            get_reconnect_status,
            # initialize_reconnect_manager,
            # start_reconnect_monitoring,
            # stop_reconnect_monitoring
        )
        
        # 获取当前状态
        status = get_reconnect_status()
        print(f"当前重连状态: {status}")
        
        print("\n集成使用步骤:")
        print("""
1. 初始化重连管理器:
   manager = initialize_reconnect_manager(wx_instance, config)

2. 启动监控:
   start_reconnect_monitoring()

3. 获取状态:
   status = get_reconnect_status()

4. 强制重连:
   success = force_reconnect()

5. 停止监控:
   stop_reconnect_monitoring()
        """)
        
        print("✅ 集成使用示例完成")
        
    except Exception as e:
        print(f"❌ 集成使用示例失败: {e}")

def show_configuration_guide():
    """显示配置指南"""
    print("\n=== 配置指南 ===")
    
    print("""
1. 运行配置向导:
   python src/wechat_reconnect/config_wizard.py

2. 手动配置邮箱 (QQ邮箱示例):
   - 登录QQ邮箱
   - 设置 → 账户 → 开启SMTP服务
   - 获取授权码
   - 在配置中填入:
     * sender_email: <EMAIL>
     * sender_password: 授权码 (不是登录密码)
     * recipient_email: 接收二维码的邮箱

3. 启动监控:
   Windows: start_wechat_monitor.bat
   Linux/Mac: ./start_wechat_monitor.sh

4. 测试功能:
   python src/wechat_reconnect/standalone_monitor.py --test-email
    """)

def main():
    """主函数"""
    print("=" * 60)
    print("           微信重连模块使用示例")
    print("=" * 60)
    
    # 运行各种示例
    example_basic_usage()
    example_standalone_monitor()
    example_email_configuration()
    example_integration()
    show_configuration_guide()
    
    print("\n" + "=" * 60)
    print("                示例完成")
    print("=" * 60)
    
    print("\n🚀 快速开始:")
    print("1. 运行配置向导: python src/wechat_reconnect/config_wizard.py")
    print("2. 启动监控: start_wechat_monitor.bat")
    print("3. 查看文档: src/wechat_reconnect/README.md")

if __name__ == "__main__":
    main()
