# Fish API配置和WebUI折叠栏实现总结

## 概述

成功为项目添加了Fish API配置选项和语音通话配置，并在WebUI配置中心实现了配置折叠栏功能。参考了语音通话版本的实现方式，提供了完整的配置管理功能。

## 实现的功能

### 1. Fish API配置
- **API密钥配置**: 支持Fish Audio API密钥的安全存储
- **模型ID配置**: 支持自定义音色模型ID
- **温度参数**: 控制语音合成的随机性 (0-1)
- **Top-P参数**: 控制语音合成的多样性 (0-1)
- **语速配置**: 控制语音播放速度 (0.5-2.0)

### 2. 语音通话配置
- **防回音功能**: 启用双虚拟音频设备防回音
- **ASR设备关键词**: 配置语音识别输入设备
- **TTS设备关键词**: 配置语音播放输出设备
- **智能设备匹配**: 支持精确匹配和相似度匹配

### 3. WebUI配置界面
- **配置折叠栏**: 所有配置按功能分组，支持展开/折叠
- **直观的界面**: 滑块控件、密码输入框、开关等
- **实时保存**: 配置修改后立即生效
- **配置验证**: 自动验证配置项的有效性

## 文件修改清单

### 配置文件
1. **src/config/config.json.template** - 添加Fish API配置模板
2. **src/config/config.json** - 添加Fish API配置实例
3. **src/config/__init__.py** - 添加配置数据类和常量

### WebUI文件
4. **src/webui/templates/config.html** - 添加Fish API配置界面
5. **src/webui/run_config_web.py** - 添加配置分组逻辑
6. **run_config_web.py** - 更新主WebUI配置处理

### 功能文件
7. **src/fish/tts_client.py** - 更新TTS客户端支持配置
8. **src/ASR/main.py** - 改进设备匹配逻辑

## 配置结构

### Fish API配置
```json
"fish_api": {
    "api_key": {
        "value": "",
        "type": "string",
        "description": "Fish Audio API密钥",
        "is_secret": true
    },
    "model_id": {
        "value": "",
        "type": "string",
        "description": "Fish Audio 模型ID（音色ID）"
    },
    "temperature": {
        "value": 0.7,
        "type": "number",
        "description": "Fish Audio 温度参数",
        "min": 0,
        "max": 1
    },
    "top_p": {
        "value": 0.7,
        "type": "number",
        "description": "Fish Audio Top-P参数",
        "min": 0,
        "max": 1
    },
    "speed": {
        "value": 1.0,
        "type": "number",
        "description": "Fish Audio 语速",
        "min": 0.5,
        "max": 2.0
    }
}
```

### 语音通话配置
```json
"voice_call": {
    "anti_echo_enabled": {
        "value": false,
        "type": "boolean",
        "description": "启用防回音功能（使用双虚拟音频设备）"
    },
    "asr_device_keyword": {
        "value": "CABLE Input",
        "type": "string",
        "description": "ASR音频输入设备关键词（用于语音识别）"
    },
    "tts_device_keyword": {
        "value": "VOICEMEETER",
        "type": "string",
        "description": "TTS音频输出设备关键词（用于语音播放）"
    }
}
```

## WebUI配置分组

现在WebUI配置中心包含以下折叠栏：

1. **基础配置** - 用户设置、LLM配置
2. **Fish API配置** - Fish Audio相关设置
3. **语音通话配置** - 音频设备和防回音设置
4. **图像识别API配置** - 视觉识别相关设置
5. **主动消息配置** - 自动消息和安静时间
6. **人设配置** - 角色设定和上下文
7. **消息配置** - 消息队列设置
8. **网络搜索配置** - 搜索功能设置
9. **天气配置** - 天气查询设置

## 使用方式

### 1. 在代码中使用配置
```python
# 方式1: 直接使用配置常量
from src.config import FISH_API_KEY, FISH_MODEL_ID, FISH_TEMPERATURE, FISH_TOP_P, FISH_SPEED

# 方式2: 通过配置对象访问
from src.config import config
api_key = config.media.fish_api.api_key
model_id = config.media.fish_api.model_id

# 方式3: 在Fish TTS客户端中自动使用
from src.fish.tts_client import FishTTSClient
client = FishTTSClient()  # 自动从配置获取参数
```

### 2. 设备匹配优先级
1. 精确关键词匹配
2. 相似度匹配（使用difflib）
3. 自动选择最合适的设备

### 3. 配置优先级
1. 函数参数（最高优先级）
2. 配置文件设置
3. 环境变量
4. 默认值（最低优先级）

## 特性亮点

1. **完整的配置管理**: 从配置文件到WebUI界面的完整链路
2. **智能设备匹配**: 支持模糊匹配，提高设备兼容性
3. **用户友好界面**: 直观的滑块、开关和输入框
4. **实时生效**: 配置修改后立即应用到系统
5. **向后兼容**: 保持与现有代码的完全兼容
6. **安全存储**: API密钥等敏感信息安全处理

## 访问地址

WebUI配置中心: http://localhost:8502/config

现在用户可以在WebUI中轻松配置Fish API参数和音频设备，系统会自动使用这些配置，并在找不到精确匹配的设备时智能选择最相似的设备。所有配置都以折叠栏的形式组织，提供了清晰的配置管理体验。
