#!/usr/bin/env python3
"""全面的TTS测试，包括多种设备选项"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_multiple_devices():
    print("=== 全面TTS设备测试 ===\n")
    
    # 测试设备列表
    test_devices = [
        "Voicemeeter Input",  # 当前配置
        "扬声器",             # 物理扬声器
        "Speakers",           # 英文扬声器
        "CABLE Input",        # 原配置
    ]
    
    for device_keyword in test_devices:
        print(f"测试设备: '{device_keyword}'")
        print("-" * 40)
        
        try:
            # 临时修改配置
            import json
            config_path = "src/config/config.json"
            
            # 读取配置
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 备份原配置
            original_device = config["categories"]["media_settings"]["settings"]["voice_call"]["tts_device_keyword"]["value"]
            
            # 临时修改
            config["categories"]["media_settings"]["settings"]["voice_call"]["tts_device_keyword"]["value"] = device_keyword
            
            # 写入临时配置
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            
            # 重新加载配置
            import importlib
            import src.config
            importlib.reload(src.config)
            
            # 测试TTS
            from src.fish.tts_client import TTSClient
            tts = TTSClient()
            
            device_idx = tts._find_output_device(device_keyword)
            if device_idx is not None:
                # 获取设备信息
                import pyaudio
                pa = pyaudio.PyAudio()
                dev_info = pa.get_device_info_by_index(device_idx)
                dev_name = dev_info["name"]
                pa.terminate()
                
                print(f"✅ 找到设备: {device_idx} - {dev_name}")
                
                # 播放测试音频
                print("🔊 播放测试音频...")
                tts.say(f"正在测试{device_keyword}设备")
                print("✅ 播放完成")
                
                # 询问用户是否听到声音
                response = input("❓ 你听到声音了吗？(y/n): ").strip().lower()
                if response == 'y':
                    print(f"🎉 成功！'{device_keyword}' 设备工作正常")
                    
                    # 恢复配置为这个工作的设备
                    config["categories"]["media_settings"]["settings"]["voice_call"]["tts_device_keyword"]["value"] = device_keyword
                    with open(config_path, 'w', encoding='utf-8') as f:
                        json.dump(config, f, ensure_ascii=False, indent=4)
                    
                    print(f"✅ 已将TTS设备配置保存为: '{device_keyword}'")
                    return True
                else:
                    print("❌ 没有听到声音")
            else:
                print(f"❌ 未找到匹配的设备")
            
            # 恢复原配置
            config["categories"]["media_settings"]["settings"]["voice_call"]["tts_device_keyword"]["value"] = original_device
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
        
        print()
    
    print("❌ 所有设备测试完成，没有找到工作的设备")
    return False

def quick_speaker_test():
    """快速测试直接输出到扬声器"""
    print("=== 快速扬声器测试 ===\n")
    
    try:
        # 临时修改配置为扬声器
        import json
        config_path = "src/config/config.json"
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 备份原配置
        original_device = config["categories"]["media_settings"]["settings"]["voice_call"]["tts_device_keyword"]["value"]
        
        # 设置为扬声器
        config["categories"]["media_settings"]["settings"]["voice_call"]["tts_device_keyword"]["value"] = "扬声器"
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=4)
        
        # 重新加载配置
        import importlib
        import src.config
        importlib.reload(src.config)
        
        # 测试TTS
        from src.fish.tts_client import TTSClient
        tts = TTSClient()
        
        print("🔊 播放测试音频到扬声器...")
        tts.say("这是扬声器测试，你应该能听到这段话")
        
        response = input("❓ 你听到声音了吗？(y/n): ").strip().lower()
        if response == 'y':
            print("🎉 扬声器工作正常！")
            
            # 询问是否保持这个配置
            keep = input("❓ 是否保持使用扬声器？(y/n): ").strip().lower()
            if keep != 'y':
                # 恢复原配置
                config["categories"]["media_settings"]["settings"]["voice_call"]["tts_device_keyword"]["value"] = original_device
                with open(config_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=4)
                print("✅ 已恢复原配置")
            else:
                print("✅ 已保存扬声器配置")
            return True
        else:
            # 恢复原配置
            config["categories"]["media_settings"]["settings"]["voice_call"]["tts_device_keyword"]["value"] = original_device
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            print("❌ 扬声器测试失败，已恢复原配置")
            return False
            
    except Exception as e:
        print(f"❌ 扬声器测试失败: {e}")
        return False

if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 快速扬声器测试")
    print("2. 全面设备测试")
    
    choice = input("请选择 (1/2): ").strip()
    
    if choice == "1":
        quick_speaker_test()
    elif choice == "2":
        test_multiple_devices()
    else:
        print("无效选择，执行快速扬声器测试...")
        quick_speaker_test()
