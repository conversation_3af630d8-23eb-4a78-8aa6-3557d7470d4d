#!/usr/bin/env python3
"""修复TTS配置建议"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def analyze_audio_setup():
    print("=== TTS音频配置分析 ===\n")
    
    # 1. 当前配置
    print("1. 当前配置:")
    try:
        from src.config import VOICE_CALL_ANTI_ECHO_ENABLED, VOICE_CALL_ASR_DEVICE_KEYWORD, VOICE_CALL_TTS_DEVICE_KEYWORD
        print(f"   防回音功能: {'启用' if VOICE_CALL_ANTI_ECHO_ENABLED else '禁用'}")
        print(f"   ASR设备关键词: '{VOICE_CALL_ASR_DEVICE_KEYWORD}'")
        print(f"   TTS设备关键词: '{VOICE_CALL_TTS_DEVICE_KEYWORD}'")
    except Exception as e:
        print(f"   配置加载失败: {e}")
        return
    print()
    
    # 2. 分析可用设备
    print("2. 推荐的设备配置:")
    
    # 获取设备列表
    import pyaudio
    pa = pyaudio.PyAudio()
    
    # 查找合适的TTS输出设备
    tts_candidates = []
    asr_candidates = []
    
    for idx in range(pa.get_device_count()):
        info = pa.get_device_info_by_index(idx)
        name = info.get("name", "")
        
        # TTS输出设备候选（需要输出通道）
        if info.get("maxOutputChannels", 0) > 0:
            if any(keyword in name.lower() for keyword in ["voicemeeter input", "扬声器", "speakers"]):
                tts_candidates.append((idx, name, info.get("maxOutputChannels", 0)))
        
        # ASR输入设备候选（需要输入通道）
        if info.get("maxInputChannels", 0) > 0:
            if any(keyword in name.lower() for keyword in ["cable output", "voicemeeter out", "麦克风", "microphone"]):
                asr_candidates.append((idx, name, info.get("maxInputChannels", 0)))
    
    pa.terminate()
    
    print("   推荐的TTS输出设备（用于播放AI语音）:")
    for idx, name, channels in tts_candidates[:5]:  # 只显示前5个
        print(f"     {idx}: {name} (输出通道: {channels})")
    
    print()
    print("   推荐的ASR输入设备（用于接收用户语音）:")
    for idx, name, channels in asr_candidates[:5]:  # 只显示前5个
        print(f"     {idx}: {name} (输入通道: {channels})")
    
    print()
    
    # 3. 配置建议
    print("3. 配置建议:")
    print("   为了防止TTS-ASR回音，建议使用以下配置:")
    print()
    print("   方案1: VoiceMeeter + VB-Cable (推荐)")
    print("     ASR输入设备: 'Voicemeeter Out'  # 接收用户语音")
    print("     TTS输出设备: 'Voicemeeter Input' # 播放AI语音")
    print("     说明: 通过VoiceMeeter路由，避免回音")
    print()
    print("   方案2: 直接使用物理设备")
    print("     ASR输入设备: '麦克风' 或 'Microphone'")
    print("     TTS输出设备: '扬声器' 或 'Speakers'")
    print("     说明: 简单但可能有回音")
    print()
    
    # 4. 当前问题诊断
    print("4. 当前问题诊断:")
    current_tts = VOICE_CALL_TTS_DEVICE_KEYWORD
    if "cable input" in current_tts.lower():
        print("   ⚠️  问题发现: TTS设备设置为 'CABLE Input'")
        print("      'CABLE Input' 是虚拟音频输入端，音频会被发送到这里")
        print("      但需要通过其他软件（如VoiceMeeter）路由到扬声器才能听到")
        print()
        print("   🔧 解决方案:")
        print("      选项1: 将TTS设备改为 'Voicemeeter Input'")
        print("      选项2: 在VoiceMeeter中设置CABLE Input的音频路由")
        print("      选项3: 直接使用物理扬声器设备")
    
    print()
    print("5. 测试建议:")
    print("   1. 先测试直接输出到扬声器: 将TTS设备改为 '扬声器'")
    print("   2. 确认能听到声音后，再配置虚拟设备防回音")
    print("   3. 使用WebUI配置页面修改设备设置")

if __name__ == "__main__":
    analyze_audio_setup()
