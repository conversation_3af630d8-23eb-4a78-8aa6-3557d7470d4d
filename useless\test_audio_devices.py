#!/usr/bin/env python3
"""检测可用的音频设备"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_audio_devices():
    print("=== 音频设备检测 ===\n")
    
    try:
        # 尝试导入sounddevice
        try:
            import sounddevice as sd
            print("✅ sounddevice可用")
            
            print("\n📱 可用的音频设备:")
            devices = sd.query_devices()
            for idx, dev in enumerate(devices):
                if dev.get("max_output_channels", 0) > 0:
                    print(f"  [{idx}] {dev['name']} (输出: {dev['max_output_channels']}通道)")
                    
            # 测试设备查找
            keywords = ["CABLE Input", "Voicemeeter Out B1", "CABLE", "Voicemeeter"]
            for keyword in keywords:
                from src.fish.tts_client import TTSClient
                device_idx = TTSClient._find_output_device(keyword)
                if device_idx is not None:
                    device_name = sd.query_devices(device_idx)["name"]
                    print(f"✅ 关键词 '{keyword}' 找到设备: [{device_idx}] {device_name}")
                else:
                    print(f"❌ 关键词 '{keyword}' 未找到设备")
                    
        except ImportError:
            print("❌ sounddevice不可用，尝试pyaudio")
            
            try:
                import pyaudio
                print("✅ pyaudio可用")
                
                pa = pyaudio.PyAudio()
                print("\n📱 可用的音频设备:")
                for idx in range(pa.get_device_count()):
                    dev = pa.get_device_info_by_index(idx)
                    if dev.get("maxOutputChannels", 0) > 0:
                        print(f"  [{idx}] {dev['name']} (输出: {dev['maxOutputChannels']}通道)")
                pa.terminate()
                
            except ImportError:
                print("❌ pyaudio也不可用")
                
    except Exception as e:
        print(f"❌ 设备检测失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_audio_devices()
