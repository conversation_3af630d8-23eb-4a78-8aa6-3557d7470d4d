@echo off
chcp 65001 >nul
title 微信重连监控器

echo ========================================
echo           微信重连监控器
echo ========================================
echo.

:menu
echo 请选择操作：
echo 1. 启动微信重连监控
echo 2. 测试邮件配置
echo 3. 强制重连微信
echo 4. 退出
echo.
set /p choice=请输入选择 (1-4): 

if "%choice%"=="1" goto start_monitor
if "%choice%"=="2" goto test_email
if "%choice%"=="3" goto force_reconnect
if "%choice%"=="4" goto exit
echo 无效选择，请重新输入
echo.
goto menu

:start_monitor
echo.
echo 正在启动微信重连监控...
echo 按 Ctrl+C 可以停止监控
echo.
python src/wechat_reconnect/standalone_monitor.py
echo.
echo 监控已停止
pause
goto menu

:test_email
echo.
echo 正在测试邮件配置...
python src/wechat_reconnect/standalone_monitor.py --test-email
echo.
pause
goto menu

:force_reconnect
echo.
echo 正在执行强制重连...
python src/wechat_reconnect/standalone_monitor.py --force-reconnect
echo.
pause
goto menu

:exit
echo 再见！
exit /b 0
