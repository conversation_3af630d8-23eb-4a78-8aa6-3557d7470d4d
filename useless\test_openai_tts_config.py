#!/usr/bin/env python3
"""测试OpenAI TTS配置是否正常工作"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_openai_tts_config():
    print("=== OpenAI TTS配置测试 ===\n")
    
    # 1. 检查新配置是否加载正常
    print("1. 检查新配置...")
    try:
        from src.config import (
            OPENAI_TTS_API_KEY, OPENAI_TTS_BASE_URL, OPENAI_TTS_MODEL, 
            OPENAI_TTS_VOICE, OPENAI_TTS_TEMPERATURE, OPENAI_TTS_TOP_P, OPENAI_TTS_SPEED
        )
        print(f"   API Key: {OPENAI_TTS_API_KEY[:10]}..." if OPENAI_TTS_API_KEY else "   API Key: 未设置")
        print(f"   Base URL: {OPENAI_TTS_BASE_URL}")
        print(f"   Model: {OPENAI_TTS_MODEL}")
        print(f"   Voice: {OPENAI_TTS_VOICE}")
        print(f"   Temperature: {OPENAI_TTS_TEMPERATURE}")
        print(f"   Top P: {OPENAI_TTS_TOP_P}")
        print(f"   Speed: {OPENAI_TTS_SPEED}")
    except Exception as e:
        print(f"   ❌ 配置加载失败: {e}")
        return False
    
    print()
    
    # 2. 检查向后兼容的配置
    print("2. 检查向后兼容配置...")
    try:
        from src.config import FISH_API_KEY, FISH_MODEL_ID, FISH_TEMPERATURE, FISH_TOP_P, FISH_SPEED
        print(f"   Fish API Key: {FISH_API_KEY[:10]}..." if FISH_API_KEY else "   Fish API Key: 未设置")
        print(f"   Fish Model ID: {FISH_MODEL_ID}")
        print(f"   Fish Temperature: {FISH_TEMPERATURE}")
        print(f"   Fish Top P: {FISH_TOP_P}")
        print(f"   Fish Speed: {FISH_SPEED}")
    except Exception as e:
        print(f"   ❌ 向后兼容配置失败: {e}")
        return False
    
    print()
    
    # 3. 测试新的TTS客户端
    print("3. 测试新的TTS客户端...")
    try:
        from src.fish.tts_client import TTSClient
        tts = TTSClient()
        print(f"   ✅ TTS客户端初始化成功")
        print(f"   API Key: {tts.api_key[:10]}...")
        print(f"   Base URL: {tts.base_url}")
        print(f"   Model: {tts.model}")
        print(f"   Voice: {tts.voice}")
        print(f"   是否Fish Audio: {tts.is_fish_audio}")
        print(f"   设备关键词: '{tts.device_keyword}'")
    except Exception as e:
        print(f"   ❌ TTS客户端初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print()
    
    # 4. 测试向后兼容的Fish TTS客户端
    print("4. 测试向后兼容的Fish TTS客户端...")
    try:
        from src.fish.tts_client import FishTTSClient
        fish_tts = FishTTSClient()
        print(f"   ✅ Fish TTS客户端初始化成功")
        print(f"   API Key: {fish_tts.api_key[:10]}...")
        print(f"   Base URL: {fish_tts.base_url}")
        print(f"   Model: {fish_tts.model}")
        print(f"   Voice: {fish_tts.voice}")
        print(f"   是否Fish Audio: {fish_tts.is_fish_audio}")
    except Exception as e:
        print(f"   ❌ Fish TTS客户端初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print()
    
    # 5. 检查SDK可用性
    print("5. 检查SDK可用性...")
    try:
        from src.fish.tts_client import FISH_SDK_AVAILABLE, OPENAI_AVAILABLE
        print(f"   Fish SDK可用: {FISH_SDK_AVAILABLE}")
        print(f"   OpenAI SDK可用: {OPENAI_AVAILABLE}")
    except Exception as e:
        print(f"   ❌ SDK检查失败: {e}")
        return False
    
    print()
    print("✅ 所有测试通过！OpenAI TTS配置工作正常。")
    return True

if __name__ == "__main__":
    test_openai_tts_config()
