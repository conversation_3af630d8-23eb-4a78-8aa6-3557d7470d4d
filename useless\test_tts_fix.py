#!/usr/bin/env python3
"""测试修复后的TTS功能"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_tts_client():
    print("=== TTS客户端修复测试 ===\n")
    
    try:
        from src.fish.tts_client import TTSClient
        print("✅ TTSClient导入成功")
        
        # 初始化TTS客户端
        tts = TTSClient()
        print("✅ TTS客户端初始化成功")
        print(f"   API Key: {tts.api_key[:10]}...")
        print(f"   Base URL: {tts.base_url}")
        print(f"   Model: {tts.model}")
        print(f"   Voice: {tts.voice}")
        print(f"   是否Fish Audio: {tts.is_fish_audio}")
        
        # 检查方法是否存在
        if hasattr(tts, '_say_with_openai'):
            print("✅ _say_with_openai方法存在")
        else:
            print("❌ _say_with_openai方法不存在")
            
        if hasattr(tts, '_say_with_fish'):
            print("✅ _say_with_fish方法存在")
        else:
            print("❌ _say_with_fish方法不存在")
            
        # 测试设备查找
        device_idx = tts._find_output_device(tts.device_keyword)
        if device_idx is not None:
            print(f"✅ 找到输出设备: {device_idx}")
        else:
            print(f"⚠️ 未找到设备关键词 '{tts.device_keyword}' 对应的设备")
            
        print("\n✅ TTS客户端修复测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ TTS客户端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_tts_client()
