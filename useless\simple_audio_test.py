#!/usr/bin/env python3
"""简单的音频测试，使用默认设备"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_default_audio():
    print("=== 默认音频设备测试 ===\n")
    
    try:
        import pyaudio
        import wave
        import numpy as np
        
        # 生成测试音频（440Hz正弦波，1秒）
        sample_rate = 44100
        duration = 1.0
        frequency = 440.0
        
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        wave_data = np.sin(frequency * 2 * np.pi * t) * 0.3
        audio_data = (wave_data * 32767).astype(np.int16)
        
        print("1. 测试默认音频设备...")
        
        # 使用PyAudio播放
        pa = pyaudio.PyAudio()
        
        # 获取默认输出设备
        default_device = pa.get_default_output_device_info()
        print(f"   默认输出设备: {default_device['name']}")
        
        stream = pa.open(
            format=pyaudio.paInt16,
            channels=1,
            rate=sample_rate,
            output=True,
            output_device_index=default_device['index']
        )
        
        print("🔊 播放测试音频（440Hz音调，1秒）...")
        stream.write(audio_data.tobytes())
        
        stream.stop_stream()
        stream.close()
        pa.terminate()
        
        print("✅ 音频播放完成")
        
        response = input("❓ 你听到440Hz音调了吗？(y/n): ").strip().lower()
        if response == 'y':
            print("🎉 默认音频设备工作正常！")
            return True
        else:
            print("❌ 默认音频设备可能有问题")
            return False
            
    except Exception as e:
        print(f"❌ 音频测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tts_with_default():
    """使用默认设备测试TTS"""
    print("\n=== TTS默认设备测试 ===\n")
    
    try:
        # 临时修改配置为默认设备（空字符串表示默认）
        import json
        config_path = "src/config/config.json"
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 备份原配置
        original_device = config["categories"]["media_settings"]["settings"]["voice_call"]["tts_device_keyword"]["value"]
        
        # 设置为空（使用默认设备）
        config["categories"]["media_settings"]["settings"]["voice_call"]["tts_device_keyword"]["value"] = ""
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=4)
        
        # 重新加载配置
        import importlib
        import src.config
        importlib.reload(src.config)
        
        # 创建自定义TTS客户端，使用默认设备
        from src.fish.tts_client import TTSClient
        
        # 修改TTS客户端使用默认设备
        class DefaultTTSClient(TTSClient):
            def _find_output_device(self, keyword):
                import pyaudio
                pa = pyaudio.PyAudio()
                default_device = pa.get_default_output_device_info()
                device_idx = default_device['index']
                pa.terminate()
                return device_idx
        
        tts = DefaultTTSClient()
        
        print("🔊 使用默认设备播放TTS...")
        tts.say("这是使用默认音频设备的TTS测试")
        
        response = input("❓ 你听到TTS语音了吗？(y/n): ").strip().lower()
        
        # 恢复原配置
        config["categories"]["media_settings"]["settings"]["voice_call"]["tts_device_keyword"]["value"] = original_device
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=4)
        
        if response == 'y':
            print("🎉 TTS默认设备工作正常！")
            
            # 询问是否使用默认设备
            use_default = input("❓ 是否配置TTS使用默认设备？(y/n): ").strip().lower()
            if use_default == 'y':
                # 设置为扬声器（通常是默认设备）
                config["categories"]["media_settings"]["settings"]["voice_call"]["tts_device_keyword"]["value"] = "扬声器"
                with open(config_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=4)
                print("✅ 已配置TTS使用扬声器设备")
            
            return True
        else:
            print("❌ TTS默认设备测试失败")
            return False
            
    except Exception as e:
        print(f"❌ TTS默认设备测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 先测试基本音频功能
    if test_default_audio():
        # 如果基本音频正常，再测试TTS
        test_tts_with_default()
    else:
        print("\n❌ 基本音频功能异常，请检查：")
        print("   1. 音响/耳机是否正确连接")
        print("   2. Windows音量是否开启")
        print("   3. 默认播放设备是否正确设置")
