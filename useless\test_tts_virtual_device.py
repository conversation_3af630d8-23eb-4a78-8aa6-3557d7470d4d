#!/usr/bin/env python3
"""测试TTS输出到虚拟音频设备"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_tts_virtual_device():
    print("=== TTS虚拟设备输出测试 ===\n")
    
    try:
        from src.fish.tts_client import TTSClient
        
        # 初始化TTS客户端
        tts = TTSClient()
        print(f"✅ TTS客户端初始化成功")
        print(f"   设备关键词: {tts.device_keyword}")
        
        # 查找设备
        device_idx = tts._find_output_device(tts.device_keyword)
        if device_idx is not None:
            print(f"✅ 找到虚拟音频设备: 索引 {device_idx}")
            
            # 获取设备名称
            try:
                import pyaudio
                pa = pyaudio.PyAudio()
                device_info = pa.get_device_info_by_index(device_idx)
                device_name = device_info['name']
                pa.terminate()
                print(f"   设备名称: {device_name}")
            except:
                print(f"   无法获取设备名称")
        else:
            print(f"❌ 未找到设备关键词: {tts.device_keyword}")
            return
            
        # 测试TTS播放到虚拟设备
        print(f"\n🎵 测试TTS播放到虚拟音频设备...")
        print(f"   目标设备: {tts.device_keyword} (索引 {device_idx})")
        test_text = "测试虚拟音频设备输出"
        tts.say(test_text)
        print("✅ TTS播放完成！音频应该已输出到虚拟音频设备")
        print("   请检查您的音频软件（如Voicemeeter）是否接收到音频信号")
        
    except Exception as e:
        print(f"❌ TTS测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_tts_virtual_device()
