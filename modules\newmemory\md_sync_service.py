"""
MD文件到数据库同步服务
负责在系统启动时将MD文件内容同步到SQLite数据库
"""

import os
import sqlite3
import logging
import hashlib
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import re

logger = logging.getLogger('main')

class MDSyncService:
    """
    MD文件到数据库同步服务
    功能：
    1. 检测MD文件变更
    2. 解析MD文件内容
    3. 同步到SQLite数据库
    4. 维护数据一致性
    """
    
    def __init__(self, root_dir: str):
        self.root_dir = root_dir
        
    def _get_file_hash(self, file_path: str) -> str:
        """计算文件MD5哈希值，用于检测文件变更"""
        try:
            with open(file_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败: {file_path}, 错误: {e}")
            return ""
    
    def _create_memory_tables(self, db_path: str):
        """创建记忆数据库表结构"""
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 核心记忆表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS core_memory (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    content TEXT NOT NULL,
                    category TEXT DEFAULT 'general',
                    importance INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 短期记忆表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS short_memory (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    user_message TEXT NOT NULL,
                    bot_reply TEXT NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    week_number INTEGER NOT NULL
                )
            ''')
            
            # 文件同步状态表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sync_status (
                    file_path TEXT PRIMARY KEY,
                    file_hash TEXT NOT NULL,
                    last_sync TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            logger.info(f"记忆数据库表创建成功: {db_path}")
            
        except Exception as e:
            logger.error(f"创建记忆数据库表失败: {e}")
    
    def _create_story_tables(self, db_path: str):
        """创建剧情数据库表结构"""
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 剧情内容表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS story_content (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    category TEXT NOT NULL,
                    content TEXT NOT NULL,
                    tags TEXT,
                    importance INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 台词表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS dialogues (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    dialogue TEXT NOT NULL,
                    context TEXT,
                    emotion TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 人物关系表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS relationships (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    character_name TEXT NOT NULL,
                    relationship_type TEXT NOT NULL,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 重要事件表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS important_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    event_name TEXT NOT NULL,
                    description TEXT NOT NULL,
                    timeline TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 文件同步状态表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sync_status (
                    file_path TEXT PRIMARY KEY,
                    file_hash TEXT NOT NULL,
                    last_sync TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            conn.commit()
            conn.close()
            logger.info(f"剧情数据库表创建成功: {db_path}")

        except Exception as e:
            logger.error(f"创建剧情数据库表失败: {e}")
    
    def sync_avatar_memory(self, avatar_name: str) -> bool:
        """同步指定角色的记忆数据"""
        try:
            avatar_dir = os.path.join(self.root_dir, "data", "avatars", avatar_name)
            memory_dir = os.path.join(avatar_dir, "memory")

            if not os.path.exists(memory_dir):
                logger.info(f"角色 {avatar_name} 没有新记忆系统目录，跳过同步")
                return True

            md_dir = os.path.join(memory_dir, "md")
            db_dir = os.path.join(memory_dir, "db")
            
            # 确保数据库目录存在
            os.makedirs(db_dir, exist_ok=True)
            
            # 同步核心记忆
            core_memory_md = os.path.join(md_dir, "core_memory.md")
            core_memory_db = os.path.join(db_dir, "core_memory.db")
            
            if os.path.exists(core_memory_md):
                self._create_memory_tables(core_memory_db)
                self._sync_core_memory_file(core_memory_md, core_memory_db)
            
            # 同步短期记忆
            short_memory_md = os.path.join(md_dir, "short_memory.md")
            short_memory_db = os.path.join(db_dir, "short_memory.db")
            
            if os.path.exists(short_memory_md):
                self._create_memory_tables(short_memory_db)
                self._sync_short_memory_file(short_memory_md, short_memory_db)
            
            logger.info(f"角色 {avatar_name} 记忆数据同步完成")
            return True
            
        except Exception as e:
            logger.error(f"同步角色 {avatar_name} 记忆数据失败: {e}")
            return False
    
    def sync_avatar_story(self, avatar_name: str) -> bool:
        """同步指定角色的剧情数据"""
        try:
            avatar_dir = os.path.join(self.root_dir, "data", "avatars", avatar_name)
            story_dir = os.path.join(avatar_dir, "word")

            if not os.path.exists(story_dir):
                logger.info(f"角色 {avatar_name} 没有剧情系统目录，跳过同步")
                return True

            md_dir = os.path.join(story_dir, "md")
            db_dir = os.path.join(story_dir, "db")
            
            # 确保数据库目录存在
            os.makedirs(db_dir, exist_ok=True)
            
            # 同步剧情内容
            story_content_md = os.path.join(md_dir, "story_content.md")
            story_db = os.path.join(db_dir, "story_knowledge.db")
            
            if os.path.exists(story_content_md):
                self._create_story_tables(story_db)
                self._sync_story_content_file(story_content_md, story_db)
            
            logger.info(f"角色 {avatar_name} 剧情数据同步完成")
            return True
            
        except Exception as e:
            logger.error(f"同步角色 {avatar_name} 剧情数据失败: {e}")
            return False
    
    def _sync_core_memory_file(self, md_path: str, db_path: str):
        """同步核心记忆MD文件到数据库"""
        try:
            # 检查文件是否需要同步
            file_hash = self._get_file_hash(md_path)
            if not self._needs_sync(md_path, file_hash, db_path):
                return

            # 读取MD文件内容
            with open(md_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 解析MD文件结构
            sections = self._parse_markdown_sections(content)

            # 连接数据库
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 清空现有数据（重新同步）
            cursor.execute('DELETE FROM core_memory')

            # 插入解析后的数据
            for section_title, section_content in sections.items():
                if section_content.strip():
                    # 从section_title中提取用户ID，格式如"用户: 1"或"用户: 睦子米"
                    user_id = 'default'
                    if section_title.startswith('用户:') or section_title.startswith('用户：'):
                        user_id = section_title.split(':', 1)[1].strip() if ':' in section_title else section_title.split('：', 1)[1].strip()

                    cursor.execute('''
                        INSERT INTO core_memory (user_id, content, category, importance)
                        VALUES (?, ?, ?, ?)
                    ''', (user_id, section_content.strip(), 'general', 1))

            conn.commit()
            conn.close()

            # 更新同步状态
            self._update_sync_status(md_path, file_hash, db_path)
            logger.info(f"核心记忆文件同步成功: {md_path}")

        except Exception as e:
            logger.error(f"同步核心记忆文件失败: {md_path}, 错误: {e}")
    
    def _sync_short_memory_file(self, md_path: str, db_path: str):
        """同步短期记忆MD文件到数据库"""
        try:
            # 检查文件是否需要同步
            file_hash = self._get_file_hash(md_path)
            if not self._needs_sync(md_path, file_hash, db_path):
                return

            # 读取MD文件内容
            with open(md_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 解析对话记录
            conversations = self._parse_conversation_records(content)

            # 连接数据库
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 清空现有数据（重新同步）
            cursor.execute('DELETE FROM short_memory')

            # 插入对话记录
            current_week = datetime.now().isocalendar()[1]
            for conv in conversations:
                # 对于短期记忆，使用通用用户ID，因为MD文件中没有用户ID信息
                # 这样所有用户都能访问到这些对话记录
                cursor.execute('''
                    INSERT INTO short_memory (user_id, user_message, bot_reply, timestamp, week_number)
                    VALUES (?, ?, ?, ?, ?)
                ''', ('*', conv['user'], conv['bot'], conv['timestamp'], current_week))

            conn.commit()
            conn.close()

            # 更新同步状态
            self._update_sync_status(md_path, file_hash, db_path)
            logger.info(f"短期记忆文件同步成功: {md_path}")

        except Exception as e:
            logger.error(f"同步短期记忆文件失败: {md_path}, 错误: {e}")
    
    def _sync_story_content_file(self, md_path: str, db_path: str):
        """同步剧情内容MD文件到数据库"""
        try:
            # 检查文件是否需要同步
            file_hash = self._get_file_hash(md_path)
            if not self._needs_sync(md_path, file_hash, db_path):
                return

            # 读取MD文件内容
            with open(md_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 解析剧情内容
            story_data = self._parse_story_content(content)

            # 连接数据库
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 清空现有数据（重新同步）
            cursor.execute('DELETE FROM story_content')
            cursor.execute('DELETE FROM dialogues')
            cursor.execute('DELETE FROM relationships')
            cursor.execute('DELETE FROM important_events')

            # 插入剧情数据
            for category, items in story_data.items():
                if category == 'dialogues':
                    for dialogue in items:
                        cursor.execute('''
                            INSERT INTO dialogues (dialogue, context, emotion)
                            VALUES (?, ?, ?)
                        ''', (dialogue.get('text', ''), dialogue.get('context', ''), dialogue.get('emotion', '')))
                elif category == 'relationships':
                    for rel in items:
                        cursor.execute('''
                            INSERT INTO relationships (character_name, relationship_type, description)
                            VALUES (?, ?, ?)
                        ''', (rel.get('name', ''), rel.get('type', ''), rel.get('description', '')))
                elif category == 'events':
                    for event in items:
                        cursor.execute('''
                            INSERT INTO important_events (event_name, description, timeline)
                            VALUES (?, ?, ?)
                        ''', (event.get('name', ''), event.get('description', ''), event.get('timeline', '')))
                else:
                    # 其他内容存入story_content表
                    for item in items:
                        cursor.execute('''
                            INSERT INTO story_content (category, content, tags, importance)
                            VALUES (?, ?, ?, ?)
                        ''', (category, str(item), '', 1))

            conn.commit()
            conn.close()

            # 更新同步状态
            self._update_sync_status(md_path, file_hash, db_path)
            logger.info(f"剧情内容文件同步成功: {md_path}")

        except Exception as e:
            logger.error(f"同步剧情内容文件失败: {md_path}, 错误: {e}")
    
    def sync_all_avatars(self) -> bool:
        """同步所有角色的数据"""
        try:
            avatars_dir = os.path.join(self.root_dir, "data", "avatars")
            if not os.path.exists(avatars_dir):
                logger.warning("角色目录不存在")
                return False
            
            success_count = 0
            total_count = 0
            
            for avatar_name in os.listdir(avatars_dir):
                avatar_path = os.path.join(avatars_dir, avatar_name)
                if os.path.isdir(avatar_path):
                    total_count += 1
                    if self.sync_avatar_memory(avatar_name) and self.sync_avatar_story(avatar_name):
                        success_count += 1
            
            logger.info(f"数据同步完成: {success_count}/{total_count} 个角色同步成功")
            return success_count == total_count

        except Exception as e:
            logger.error(f"同步所有角色数据失败: {e}")
            return False

    def _needs_sync(self, md_path: str, file_hash: str, db_path: str) -> bool:
        """检查文件是否需要同步"""
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            cursor.execute('SELECT file_hash FROM sync_status WHERE file_path = ?', (md_path,))
            result = cursor.fetchone()
            conn.close()

            if result is None or result[0] != file_hash:
                return True
            return False

        except Exception:
            return True  # 如果检查失败，默认需要同步

    def _update_sync_status(self, md_path: str, file_hash: str, db_path: str):
        """更新文件同步状态"""
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO sync_status (file_path, file_hash, last_sync)
                VALUES (?, ?, ?)
            ''', (md_path, file_hash, datetime.now()))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"更新同步状态失败: {e}")

    def _parse_markdown_sections(self, content: str) -> Dict[str, str]:
        """解析Markdown文件的章节内容"""
        sections = {}
        current_section = ""
        current_content = []

        lines = content.split('\n')
        for line in lines:
            if line.startswith('## '):
                # 保存上一个章节
                if current_section:
                    sections[current_section] = '\n'.join(current_content)

                # 开始新章节
                current_section = line[3:].strip()
                current_content = []
            elif current_section:
                current_content.append(line)

        # 保存最后一个章节
        if current_section:
            sections[current_section] = '\n'.join(current_content)

        return sections

    def _parse_conversation_records(self, content: str) -> List[Dict]:
        """解析对话记录"""
        conversations = []
        try:
            # 新记忆系统的MD文件是JSON数组格式
            import json
            if content.strip():
                records = json.loads(content.strip())
                for record in records:
                    if isinstance(record, dict) and 'user' in record and 'bot' in record:
                        conversations.append({
                            'user': record['user'],
                            'bot': record['bot'],
                            'timestamp': record.get('timestamp', datetime.now().isoformat())
                        })
                logger.info(f"解析到 {len(conversations)} 条对话记录")
        except (json.JSONDecodeError, Exception) as e:
            logger.warning(f"解析对话记录失败: {e}")

        return conversations

    def _parse_story_content(self, content: str) -> Dict[str, List]:
        """解析剧情内容"""
        story_data = {
            'dialogues': [],
            'relationships': [],
            'events': [],
            'background': [],
            'plots': []
        }

        sections = self._parse_markdown_sections(content)

        for section_title, section_content in sections.items():
            title_lower = section_title.lower()
            if '台词' in title_lower or 'dialogue' in title_lower:
                # 解析台词
                dialogues = re.findall(r'- "(.*?)"', section_content)
                for dialogue in dialogues:
                    story_data['dialogues'].append({'text': dialogue, 'context': '', 'emotion': ''})
            elif '关系' in title_lower or 'relationship' in title_lower:
                # 解析人物关系
                relationships = re.findall(r'- (.*?): (.*)', section_content)
                for name, desc in relationships:
                    story_data['relationships'].append({'name': name, 'type': '朋友', 'description': desc})
            elif '事件' in title_lower or 'event' in title_lower:
                # 解析重要事件
                events = re.findall(r'- (.*)', section_content)
                for event in events:
                    story_data['events'].append({'name': event, 'description': event, 'timeline': ''})
            else:
                # 其他内容
                story_data['background'].append(section_content)

        return story_data
