#!/usr/bin/env python3
"""
测试多模态LLM降级到Moonshot图片识别的功能
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_fallback_logic():
    """测试降级逻辑"""
    print("=" * 60)
    print("测试多模态LLM降级到Moonshot图片识别功能")
    print("=" * 60)
    
    # 模拟图片识别服务
    class MockImageRecognitionService:
        def recognize_image(self, image_path: str, is_emoji: bool = False) -> str:
            """模拟Moonshot图片识别"""
            if is_emoji:
                return "用户发送了一张表情包，表情包的内容是：可爱的猫咪表情，表达开心的情绪"
            else:
                return "用户发送了一张照片，照片的内容是：一只橘色的小猫坐在阳光下"
    
    # 模拟LLM服务
    class MockLLMService:
        def get_response(self, message: str, user_id: str, system_prompt: str) -> str:
            """模拟LLM回复"""
            return f"根据图片内容回复：{message[:50]}..."
    
    # 模拟多模态LLM降级逻辑
    def simulate_multimodal_llm_with_fallback(text_content: str, image_path: str, 
                                            image_recognition_service, llm_service,
                                            simulate_failure: bool = False):
        """模拟多模态LLM调用和降级逻辑"""
        try:
            if simulate_failure:
                raise Exception("API请求失败: 503 - The model is overloaded")
            
            # 正常情况下的多模态LLM处理
            return "多模态LLM正常回复：我看到了图片内容，这是一个很有趣的表情包！"
            
        except Exception as e:
            print(f"多模态LLM处理失败: {str(e)}")
            
            # 降级到Moonshot图片识别服务
            if image_recognition_service:
                print("降级到Moonshot图片识别服务")
                try:
                    # 判断是否为表情包
                    is_emoji = (text_content and "表情包" in text_content) or "screenshot" in image_path.lower()
                    recognized_text = image_recognition_service.recognize_image(image_path, is_emoji)
                    
                    # 如果有文字内容，将识别结果与文字内容结合
                    if text_content:
                        combined_message = f"{text_content} {recognized_text}"
                    else:
                        combined_message = recognized_text
                    
                    # 使用普通LLM处理组合后的消息
                    return llm_service.get_response(combined_message, "test_user", "system_prompt")
                    
                except Exception as fallback_error:
                    print(f"Moonshot图片识别降级也失败: {str(fallback_error)}")
            
            # 最终降级到纯文字处理
            fallback_message = text_content if text_content else "用户发送了一张图片"
            return llm_service.get_response(fallback_message, "test_user", "system_prompt")
    
    # 创建模拟服务实例
    image_recognition_service = MockImageRecognitionService()
    llm_service = MockLLMService()
    
    print(f"测试时间: {datetime.now()}")
    print()
    
    # 测试用例1: 多模态LLM正常工作
    print("测试1: 多模态LLM正常工作")
    print("-" * 40)
    text_content1 = "你看得到这个表情包吗"
    image_path1 = "F:\\test\\yuy\\screenshot\\睦子米_20250710224857.png"
    result1 = simulate_multimodal_llm_with_fallback(
        text_content1, image_path1, image_recognition_service, llm_service, simulate_failure=False
    )
    print(f"输入文字: {text_content1}")
    print(f"图片路径: {image_path1}")
    print(f"结果: {result1}")
    print()
    
    # 测试用例2: 多模态LLM失败，降级到Moonshot（表情包）
    print("测试2: 多模态LLM失败，降级到Moonshot（表情包）")
    print("-" * 40)
    text_content2 = "你看得到这个表情包吗"
    image_path2 = "F:\\test\\yuy\\screenshot\\睦子米_20250710224857.png"
    result2 = simulate_multimodal_llm_with_fallback(
        text_content2, image_path2, image_recognition_service, llm_service, simulate_failure=True
    )
    print(f"输入文字: {text_content2}")
    print(f"图片路径: {image_path2}")
    print(f"结果: {result2}")
    print()
    
    # 测试用例3: 多模态LLM失败，降级到Moonshot（普通图片）
    print("测试3: 多模态LLM失败，降级到Moonshot（普通图片）")
    print("-" * 40)
    text_content3 = "这是什么"
    image_path3 = "F:\\test\\yuy\\images\\cat.jpg"
    result3 = simulate_multimodal_llm_with_fallback(
        text_content3, image_path3, image_recognition_service, llm_service, simulate_failure=True
    )
    print(f"输入文字: {text_content3}")
    print(f"图片路径: {image_path3}")
    print(f"结果: {result3}")
    print()
    
    # 测试用例4: 纯图片消息，多模态LLM失败
    print("测试4: 纯图片消息，多模态LLM失败")
    print("-" * 40)
    text_content4 = ""
    image_path4 = "F:\\test\\yuy\\screenshot\\表情包.png"
    result4 = simulate_multimodal_llm_with_fallback(
        text_content4, image_path4, image_recognition_service, llm_service, simulate_failure=True
    )
    print(f"输入文字: {text_content4 or '(无文字)'}")
    print(f"图片路径: {image_path4}")
    print(f"结果: {result4}")
    print()

def test_timeout_logic():
    """测试消息队列超时逻辑修复"""
    print("=" * 60)
    print("测试消息队列超时逻辑修复")
    print("=" * 60)
    
    def calculate_smart_timeout(is_emoji_message: bool, QUEUE_TIMEOUT: int = 8) -> float:
        """模拟修复后的智能超时计算"""
        # 如果当前是表情包消息
        if is_emoji_message:
            # 如果前面有文字消息，快速处理（文字+表情包组合）
            # 这里简化处理，假设有文字消息
            print("[消息队列] 检测到文字+表情包组合，缩短等待时间")
            return 2.0  # 2秒快速处理
        else:
            # 如果当前是文字消息，使用配置的标准等待时间
            print("[消息队列] 文字消息，使用标准等待时间")
            return QUEUE_TIMEOUT
    
    print("配置的队列超时时间: 8秒")
    print()
    
    # 测试文字消息
    timeout1 = calculate_smart_timeout(is_emoji_message=False)
    print(f"文字消息超时时间: {timeout1}秒")
    print()
    
    # 测试表情包消息
    timeout2 = calculate_smart_timeout(is_emoji_message=True)
    print(f"表情包消息超时时间: {timeout2}秒")
    print()

if __name__ == "__main__":
    print("开始测试降级功能...")
    print()
    
    try:
        test_fallback_logic()
        test_timeout_logic()
        
        print("=" * 60)
        print("所有测试完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
