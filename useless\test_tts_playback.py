#!/usr/bin/env python3
"""测试TTS音频播放"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_tts_playback():
    print("=== TTS音频播放测试 ===\n")
    
    try:
        from src.fish.tts_client import TTSClient
        
        print("1. 初始化TTS客户端...")
        tts = TTSClient()
        print(f"   ✅ TTS客户端初始化成功")
        print(f"   API Key: {tts.api_key[:10]}...")
        print(f"   Model ID: {tts.model_id}")
        print(f"   设备关键词: '{tts.device_keyword}'")
        print()
        
        print("2. 查找输出设备...")
        device_idx = tts._find_output_device(tts.device_keyword)
        if device_idx is not None:
            # 获取设备名称
            import pyaudio
            pa = pyaudio.PyAudio()
            dev_info = pa.get_device_info_by_index(device_idx)
            dev_name = dev_info["name"]
            pa.terminate()
            
            print(f"   ✅ 找到输出设备: {device_idx} - {dev_name}")
            print(f"   输出通道数: {dev_info['maxOutputChannels']}")
        else:
            print(f"   ❌ 未找到匹配的输出设备")
            return
        print()
        
        print("3. 测试音频播放...")
        print("   正在播放测试语音...")
        try:
            tts.say("你好，这是TTS音频播放测试")
            print("   ✅ 音频播放完成")
        except Exception as e:
            print(f"   ❌ 音频播放失败: {e}")
            import traceback
            traceback.print_exc()
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_tts_playback()
