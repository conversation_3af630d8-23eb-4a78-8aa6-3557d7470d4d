#!/usr/bin/env python3
"""
测试微信通话窗口检测功能 - 改进版本
"""

import sys
import os
import time
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.voice.call_session import WeChatCallWindowDetector

def test_improved_detection():
    """测试改进的窗口检测功能"""
    print("=== 改进版微信通话窗口检测测试 ===")

    detector = WeChatCallWindowDetector()

    print("第一步：设置基线窗口...")
    detector.start_call_detection()
    print(f"已记录基线窗口数量: {len(detector.baseline_windows)}")

    print("\n第二步：请在5秒内发起一个微信语音通话...")
    for i in range(5, 0, -1):
        print(f"倒计时: {i}")
        time.sleep(1)

    print("\n第三步：开始检测新的通话窗口...")

    try:
        detection_count = 0
        max_detections = 30  # 最多检测30次（60秒）

        while detection_count < max_detections:
            detection_count += 1

            # 检测新的通话窗口
            new_window = detector.detect_new_call_window()
            if new_window:
                hwnd, title = new_window
                print(f"🎉 检测到新的通话窗口: {title} (ID: {hwnd})")
                print(f"   开始跟踪窗口ID: {hwnd}")
                break

            # 检查当前通话状态
            is_active = detector.is_call_active()
            if detector.tracked_call_window_id:
                print(f"📞 跟踪窗口 {detector.tracked_call_window_id}: {'活跃' if is_active else '已结束'}")
            else:
                print(f"🔍 检测中... ({detection_count}/{max_detections})")

            time.sleep(2)

        if detection_count >= max_detections:
            print("⚠️ 未在指定时间内检测到通话窗口")
        else:
            print("\n第四步：持续监控通话状态...")
            print("请挂断通话来测试检测功能，按 Ctrl+C 退出测试")

            while True:
                is_active = detector.is_call_active()
                if detector.tracked_call_window_id:
                    if is_active:
                        print(f"📞 通话进行中 (窗口ID: {detector.tracked_call_window_id})")
                    else:
                        print("📴 通话已结束！")
                        break
                else:
                    print("❌ 失去窗口跟踪")
                    break

                time.sleep(2)

    except KeyboardInterrupt:
        print("\n测试被用户中断")
    finally:
        detector.stop_call_detection()
        print("测试结束，已清理检测器状态")

def test_window_enumeration():
    """测试窗口枚举功能"""
    print("=== 窗口枚举测试 ===")

    detector = WeChatCallWindowDetector()
    windows = detector.get_all_windows()

    print(f"当前共有 {len(windows)} 个可见窗口:")
    for hwnd, title in windows.items():
        if title.strip():  # 只显示有标题的窗口
            print(f"  ID: {hwnd} - 标题: {title}")

if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    print("选择测试模式:")
    print("1. 改进版通话检测测试")
    print("2. 窗口枚举测试")

    choice = input("请输入选择 (1 或 2): ").strip()

    if choice == "1":
        test_improved_detection()
    elif choice == "2":
        test_window_enumeration()
    else:
        print("无效选择，运行改进版检测测试...")
        test_improved_detection()
