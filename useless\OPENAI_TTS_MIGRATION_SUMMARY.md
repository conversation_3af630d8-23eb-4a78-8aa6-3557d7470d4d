# OpenAI TTS API格式迁移总结

## 概述

成功将项目的Fish API配置改造为通用的OpenAI API格式，同时保持向后兼容性。现在支持配置API Base URL、API Key和模型名称，使其能够兼容各种TTS服务提供商。

## 主要改动

### 1. 配置文件结构更新

**新的配置格式 (OpenAI标准):**
```json
"openai_tts": {
    "api_key": {
        "value": "",
        "type": "string",
        "description": "OpenAI兼容TTS API密钥",
        "is_secret": true
    },
    "base_url": {
        "value": "https://api.fish.audio/v1",
        "type": "string",
        "description": "OpenAI兼容TTS API基础URL"
    },
    "model": {
        "value": "tts-1",
        "type": "string",
        "description": "TTS模型名称或ID"
    },
    "voice": {
        "value": "",
        "type": "string",
        "description": "语音音色名称或ID"
    },
    "temperature": {
        "value": 0.7,
        "type": "number",
        "description": "TTS 温度参数",
        "min": 0,
        "max": 1
    },
    "top_p": {
        "value": 0.7,
        "type": "number",
        "description": "TTS Top-P参数",
        "min": 0,
        "max": 1
    },
    "speed": {
        "value": 1.0,
        "type": "number",
        "description": "TTS 语速",
        "min": 0.5,
        "max": 2.0
    }
}
```

### 2. TTS客户端重构

**新的TTSClient特性:**
- 支持OpenAI标准配置格式
- 自动检测服务类型（Fish Audio vs OpenAI）
- 保持Fish Audio SDK的完整功能
- 支持OpenAI兼容的TTS服务
- 向后兼容的FishTTSClient别名

**使用方式:**
```python
# 新的标准方式
from src.fish.tts_client import TTSClient
tts = TTSClient()  # 自动从配置读取OpenAI格式参数
tts.say("你好，世界")

# 向后兼容方式
from src.fish.tts_client import FishTTSClient
fish_tts = FishTTSClient()  # 兼容旧的参数格式
fish_tts.say("你好，世界")
```

### 3. WebUI配置界面更新

**新的"TTS配置"选项:**
- **API密钥**: TTS API密钥，兼容Fish Audio等服务
- **API基础URL**: TTS API基础URL，兼容Fish Audio、OpenAI等服务
- **模型名称**: TTS模型名称或ID（Fish Audio优先）
- **语音音色**: 语音音色名称或ID（Fish Audio使用音色ID）
- **温度参数**: TTS创造性参数
- **Top-P参数**: TTS多样性参数
- **语速**: TTS播放速度

### 4. 向后兼容性

**保持兼容的配置常量:**
```python
# 这些常量仍然可用，映射到新的OpenAI配置
FISH_API_KEY = config.media.openai_tts.api_key
FISH_MODEL_ID = config.media.openai_tts.voice
FISH_TEMPERATURE = config.media.openai_tts.temperature
FISH_TOP_P = config.media.openai_tts.top_p
FISH_SPEED = config.media.openai_tts.speed
```

## 支持的TTS服务

### 1. Fish Audio (默认)
- **Base URL**: `https://api.fish.audio/v1`
- **模型**: 使用您的Fish Audio模型ID
- **音色**: 使用您的Fish Audio音色ID
- **特性**: 完整的Fish Audio SDK功能

### 2. OpenAI TTS
- **Base URL**: `https://api.openai.com/v1`
- **模型**: `tts-1` 或 `tts-1-hd`
- **音色**: `alloy`, `echo`, `fable`, `onyx`, `nova`, `shimmer`
- **特性**: 标准OpenAI TTS API

### 3. 其他兼容服务
- 任何支持OpenAI TTS API格式的服务
- 只需配置相应的Base URL和认证信息

## 配置示例

### Fish Audio配置
```json
{
    "api_key": "your-fish-api-key",
    "base_url": "https://api.fish.audio/v1",
    "model": "tts-1",
    "voice": "your-fish-voice-id",
    "temperature": 0.7,
    "top_p": 0.7,
    "speed": 1.0
}
```

### OpenAI配置
```json
{
    "api_key": "your-openai-api-key",
    "base_url": "https://api.openai.com/v1",
    "model": "tts-1-hd",
    "voice": "nova",
    "temperature": 0.7,
    "top_p": 0.7,
    "speed": 1.0
}
```

## 测试验证

运行测试脚本验证配置：
```bash
.venv\Scripts\python test_openai_tts_config.py
```

## 访问配置界面

WebUI配置中心: http://localhost:8502/config

在"TTS配置"部分可以找到所有新的配置选项，优先支持Fish Audio服务。

## 迁移指南

1. **现有用户**: 无需任何操作，系统自动兼容
2. **新用户**: 可以使用新的OpenAI标准格式配置
3. **切换服务**: 只需修改Base URL和相关参数即可

## 技术优势

1. **标准化**: 使用OpenAI标准API格式
2. **灵活性**: 支持多种TTS服务提供商
3. **兼容性**: 完全向后兼容现有代码
4. **可扩展**: 易于添加新的TTS服务支持
5. **用户友好**: 直观的WebUI配置界面

这次迁移为项目提供了更好的灵活性和可扩展性，同时保持了现有功能的完整性。
