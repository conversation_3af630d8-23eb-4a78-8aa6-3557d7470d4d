#!/usr/bin/env python3
"""
测试双虚拟音频设备配置
验证ASR和TTS使用不同的音频设备，避免回音问题
"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_audio_devices():
    """测试音频设备配置"""
    print("=== 双虚拟音频设备配置测试 ===\n")
    
    # 测试配置加载
    print("1. 测试配置加载...")
    try:
        from src.config import (
            VOICE_CALL_ANTI_ECHO_ENABLED, 
            VOICE_CALL_ASR_DEVICE_KEYWORD, 
            VOICE_CALL_TTS_DEVICE_KEYWORD
        )
        print(f"✅ 配置加载成功")
        print(f"   防回音功能: {'启用' if VOICE_CALL_ANTI_ECHO_ENABLED else '禁用'}")
        print(f"   ASR设备关键词: {VOICE_CALL_ASR_DEVICE_KEYWORD}")
        print(f"   TTS设备关键词: {VOICE_CALL_TTS_DEVICE_KEYWORD}")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False
    
    print()
    
    # 测试ASR设备配置
    print("2. 测试ASR设备配置...")
    try:
        from src.ASR.main import get_asr_device_keywords
        asr_keywords = get_asr_device_keywords()
        print(f"✅ ASR设备关键词: {asr_keywords}")
        
        if VOICE_CALL_ANTI_ECHO_ENABLED:
            if VOICE_CALL_ASR_DEVICE_KEYWORD in asr_keywords:
                print(f"✅ 防回音模式：ASR使用专用设备 '{VOICE_CALL_ASR_DEVICE_KEYWORD}'")
            else:
                print(f"⚠️ 配置不一致：ASR关键词中未包含配置的设备")
        else:
            print(f"ℹ️ 标准模式：ASR使用默认设备关键词")
            
    except Exception as e:
        print(f"❌ ASR设备配置测试失败: {e}")
        return False
    
    print()
    
    # 测试TTS设备配置
    print("3. 测试TTS设备配置...")
    try:
        from src.fish.tts_client import TTSClient
        tts_client = TTSClient()
        print(f"✅ TTS设备关键词: {tts_client.device_keyword}")
        
        if VOICE_CALL_ANTI_ECHO_ENABLED:
            if tts_client.device_keyword == VOICE_CALL_TTS_DEVICE_KEYWORD:
                print(f"✅ 防回音模式：TTS使用专用设备 '{VOICE_CALL_TTS_DEVICE_KEYWORD}'")
            else:
                print(f"⚠️ 配置不一致：TTS设备关键词与配置不符")
        else:
            print(f"ℹ️ 标准模式：TTS使用默认设备关键词")
            
    except Exception as e:
        print(f"❌ TTS设备配置测试失败: {e}")
        return False
    
    print()
    
    # 检查设备分离
    print("4. 检查设备分离...")
    if VOICE_CALL_ANTI_ECHO_ENABLED:
        if VOICE_CALL_ASR_DEVICE_KEYWORD != VOICE_CALL_TTS_DEVICE_KEYWORD:
            print(f"✅ 设备已分离：ASR({VOICE_CALL_ASR_DEVICE_KEYWORD}) ≠ TTS({VOICE_CALL_TTS_DEVICE_KEYWORD})")
            print(f"✅ 这将有效防止TTS音频被ASR重新捕获")
        else:
            print(f"⚠️ 设备未分离：ASR和TTS使用相同设备，可能仍有回音问题")
    else:
        print(f"ℹ️ 防回音功能未启用，使用默认配置")
    
    print()
    
    # 设备检测
    print("5. 检测可用音频设备...")
    try:
        # 检测输入设备
        print("   输入设备:")
        try:
            import pyaudio
            pa = pyaudio.PyAudio()
            for i in range(pa.get_device_count()):
                info = pa.get_device_info_by_index(i)
                if info.get('maxInputChannels', 0) > 0:
                    name = info.get('name', '')
                    if any(keyword.lower() in name.lower() for keyword in [VOICE_CALL_ASR_DEVICE_KEYWORD, 'cable', 'voicemeeter']):
                        print(f"     ✅ {name} (匹配ASR关键词)")
                    else:
                        print(f"     - {name}")
            pa.terminate()
        except Exception as e:
            print(f"     ❌ 无法检测输入设备: {e}")
        
        # 检测输出设备
        print("   输出设备:")
        try:
            import sounddevice as sd
            for idx, dev in enumerate(sd.query_devices()):
                if dev.get("max_output_channels", 0) > 0:
                    name = dev.get("name", "")
                    if any(keyword.lower() in name.lower() for keyword in [VOICE_CALL_TTS_DEVICE_KEYWORD, 'cable', 'voicemeeter']):
                        print(f"     ✅ {name} (匹配TTS关键词)")
                    else:
                        print(f"     - {name}")
        except Exception as e:
            print(f"     ❌ 无法检测输出设备: {e}")
            
    except Exception as e:
        print(f"❌ 设备检测失败: {e}")
    
    print()
    
    # 配置建议
    print("6. 配置建议...")
    if VOICE_CALL_ANTI_ECHO_ENABLED:
        print("✅ 当前配置：防回音模式")
        print("   建议的设备配置：")
        print("   • ASR输入：VB-Audio Virtual Cable")
        print("   • TTS输出：VOICEMEETER Input")
        print("   • 确保两个设备使用不同的虚拟音频通道")
    else:
        print("ℹ️ 当前配置：标准模式")
        print("   如需防止TTS-ASR回音，建议：")
        print("   1. 安装 VB-Audio Virtual Cable 和 VoiceMeeter")
        print("   2. 在WebUI中启用防回音功能")
        print("   3. 配置不同的设备关键词")
    
    return True

def main():
    """主函数"""
    try:
        success = test_audio_devices()
        if success:
            print("\n🎉 双虚拟音频设备配置测试完成！")
        else:
            print("\n❌ 测试过程中发现问题，请检查配置")
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
