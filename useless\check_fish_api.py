#!/usr/bin/env python3
"""检查Fish API连接状态"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_fish_api():
    print("=== Fish API 连接检查 ===\n")
    
    try:
        from src.config import FISH_API_KEY, FISH_MODEL_ID, FISH_TEMPERATURE, FISH_TOP_P, FISH_SPEED
        
        print("1. 配置检查:")
        print(f"   API Key: {FISH_API_KEY[:10]}..." if FISH_API_KEY else "   API Key: 未设置")
        print(f"   Model ID: {FISH_MODEL_ID}")
        print(f"   Temperature: {FISH_TEMPERATURE}")
        print(f"   Top P: {FISH_TOP_P}")
        print(f"   Speed: {FISH_SPEED}")
        print()
        
        if not FISH_API_KEY:
            print("❌ Fish API Key 未设置")
            return False
        
        print("2. 测试API连接...")
        from fish_audio_sdk import WebSocketSession, TTSRequest
        
        session = WebSocketSession(FISH_API_KEY)
        request = TTSRequest(
            text="",
            reference_id=FISH_MODEL_ID,
            format="pcm",
            sample_rate=44100,
            temperature=FISH_TEMPERATURE,
            top_p=FISH_TOP_P,
            prosody={
                "speed": FISH_SPEED,
                "volume": 0.0,
            },
        )
        
        def text_iter():
            yield "API连接测试"
        
        print("   正在连接Fish API...")
        audio_chunks = []
        for chunk in session.tts(request, text_iter(), backend="speech-1.6"):
            if chunk:
                audio_chunks.append(chunk)
        
        total_size = sum(len(chunk) for chunk in audio_chunks)
        print(f"   ✅ API连接成功，生成音频数据: {total_size} 字节")
        
        if total_size == 0:
            print("   ⚠️  警告: 生成的音频数据为空")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ API连接失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    check_fish_api()
