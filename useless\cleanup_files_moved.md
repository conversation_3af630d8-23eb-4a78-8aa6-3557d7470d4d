# 清理的无用文件列表

## 2025-07-04 清理记录

### 调试和测试文件
- `debug_config_groups.py` - 配置分组调试脚本
- `test_config_api.html` - 配置API测试页面
- `test_openai_tts_config.py` - OpenAI TTS配置测试脚本

### 文档文件
- `OPENAI_TTS_MIGRATION_SUMMARY.md` - OpenAI TTS迁移总结文档

### 损坏的文件
- `src/webui/run_config_web.py` - 损坏的WebUI配置文件（缺少导入和app定义）

### 备份文件
- `src/config/config.json.template.bak` - 配置模板备份文件（已存在于useless目录）

### 空文件
- `version.json` - 空的版本文件

### 日志文件
- `logs/bot_20250703.log` - 旧的机器人日志文件

### 缓存文件（已删除）
- `src/__pycache__/` - Python缓存目录
- `src/config/__pycache__/` - 配置模块缓存目录
- `src/webui/routes/__pycache__/` - WebUI路由缓存目录

## 清理总结

✅ **已移动到useless目录的文件**: 8个
✅ **已删除的缓存目录**: 3个
✅ **项目结构**: 更加整洁，便于维护

这些文件已被移动到useless目录或删除，以保持项目结构的整洁。所有核心功能文件都保持完整。
