# 🎯 双虚拟音频设备防回音配置指南

## 🚨 问题背景

在语音通话功能中，TTS播放的音频会被ASR重新捕获，形成无限循环：
```
用户语音 → ASR → LLM → TTS播放 → 被ASR再次识别 → LLM → TTS播放 → 无限循环 💸
```

这会导致：
- 🔥 疯狂消耗API余额
- 🔄 无限循环对话
- 🎵 音频反馈和回音

## 💡 解决方案：双虚拟音频设备

使用两个独立的虚拟音频设备，物理隔离ASR输入和TTS输出：

```
ASR输入设备：VB-Audio Virtual Cable
TTS输出设备：VOICEMEETER Input
```

## 🛠️ 安装步骤

### 1. 安装 VB-Audio Virtual Cable
1. 下载：https://vb-audio.com/Cable/
2. 安装后重启电脑
3. 验证：在Windows声音设置中应该看到 "CABLE Input" 和 "CABLE Output"

### 2. 安装 VoiceMeeter
1. 下载：https://vb-audio.com/Voicemeeter/
2. 安装 VoiceMeeter (基础版即可)
3. 验证：在Windows声音设置中应该看到 "VoiceMeeter Input" 和 "VoiceMeeter Output"

## ⚙️ 配置步骤

### 1. 在WebUI中配置
打开配置页面，找到 "媒体设置" → "语音通话" 部分：

```
✅ 启用防回音功能（使用双虚拟音频设备）
ASR音频输入设备关键词：CABLE Input
TTS音频输出设备关键词：VOICEMEETER
```

### 2. 设备关键词说明

#### ASR设备关键词（推荐值）
- `CABLE Input` - VB-Audio Virtual Cable 输入
- `VB-Audio` - VB-Audio Virtual Cable 通用
- `VOICEMEETER` - VoiceMeeter 输入（如果只有一个设备）

#### TTS设备关键词（推荐值）
- `VOICEMEETER` - VoiceMeeter 输入
- `VB-Audio` - VB-Audio Virtual Cable（如果有多个通道）
- `CABLE` - VB-Audio Virtual Cable 通用

### 3. 推荐配置组合

#### 组合1：VB-Cable + VoiceMeeter（推荐）
```
ASR输入：CABLE Input
TTS输出：VOICEMEETER
```

#### 组合2：双VB-Cable通道
```
ASR输入：CABLE Input
TTS输出：CABLE Output
```

## 🧪 测试验证

运行测试脚本验证配置：
```bash
python test_dual_audio_devices.py
```

测试内容：
- ✅ 配置加载正确
- ✅ ASR使用指定设备
- ✅ TTS使用指定设备
- ✅ 设备已分离（不同设备）
- ✅ 检测可用音频设备

## 🔧 故障排除

### 问题1：设备未找到
**现象**：程序提示找不到指定的音频设备
**解决**：
1. 检查VB-Audio和VoiceMeeter是否正确安装
2. 重启电脑确保驱动加载
3. 在Windows声音设置中确认设备存在
4. 调整设备关键词匹配实际设备名称

### 问题2：仍有回音
**现象**：配置后仍然出现TTS-ASR循环
**解决**：
1. 确认两个设备关键词不同
2. 检查设备是否真的使用了不同的虚拟通道
3. 在VoiceMeeter中检查音频路由设置

### 问题3：音频质量问题
**现象**：音频播放有杂音或延迟
**解决**：
1. 调整VoiceMeeter的缓冲区设置
2. 检查采样率设置是否一致
3. 关闭不必要的音频增强功能

## 📊 配置对比

| 配置模式 | ASR设备 | TTS设备 | 回音风险 | 配置复杂度 |
|---------|---------|---------|----------|------------|
| 单设备模式 | VB-Audio | VB-Audio | ⚠️ 高 | 🟢 简单 |
| 双设备模式 | CABLE Input | VOICEMEETER | ✅ 无 | 🟡 中等 |

## 🎯 最佳实践

### 1. 设备命名规范
- 使用明确的关键词，避免歧义
- 优先使用设备的特征性名称部分
- 避免使用过于通用的关键词

### 2. 测试流程
1. 配置完成后立即运行测试脚本
2. 进行实际语音通话测试
3. 监控API消耗，确认无异常循环

### 3. 维护建议
- 定期检查虚拟音频设备状态
- 系统更新后重新验证配置
- 备份工作配置以便快速恢复

## 🚀 使用效果

配置成功后：
- ✅ TTS音频不会被ASR重新捕获
- ✅ 完全消除无限循环问题
- ✅ API消耗恢复正常
- ✅ 支持自然的语音打断
- ✅ 更好的通话体验

现在你可以放心使用语音通话功能，不用担心API余额被恶意消耗了！🎉
