#!/usr/bin/env python3
"""最终TTS测试"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_tts_final():
    print("=== 最终TTS测试 ===\n")
    
    try:
        from src.fish.tts_client import TTSClient
        
        # 初始化TTS客户端
        tts = TTSClient()
        print(f"✅ TTS客户端初始化成功")
        
        # 测试TTS调用
        print("\n🎵 测试TTS播放...")
        test_text = "你好，这是一个测试"
        tts.say(test_text)
        print("✅ TTS调用完成！")
        
    except Exception as e:
        print(f"❌ TTS测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_tts_final()
