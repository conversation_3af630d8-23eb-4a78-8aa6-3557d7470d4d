"""
记忆系统管理器
统一管理新旧记忆系统和剧情系统的切换和调用
"""

import os
import logging
from datetime import datetime
from typing import List, Dict, Optional
from src.config import config
from modules.memory.memory_service import MemoryService
from modules.newmemory.database_memory_service import DatabaseMemoryService
from modules.newmemory.sync_initializer import SyncInitializer
from modules.story.story_service import StoryService
from modules.memory.memory_recall_service import MemoryRecallService

logger = logging.getLogger('main')

class MemoryManager:
    """
    记忆系统管理器
    功能：
    1. 根据配置选择记忆系统类型
    2. 统一的记忆接口
    3. 剧情系统集成
    4. 系统初始化和同步
    """
    
    def __init__(self, root_dir: str, api_key: str, base_url: str, model: str, max_token: int, temperature: float, max_groups: int = 10):
        self.root_dir = root_dir
        self.api_key = api_key
        self.base_url = base_url
        self.model = model
        self.max_token = max_token
        self.temperature = temperature
        self.max_groups = max_groups

        # 添加兼容性属性
        self.class_name = "MemoryManager"

        # 初始化记忆服务
        self.old_memory_service = None
        self.new_memory_service = None
        self.story_service = None
        self.sync_initializer = None
        self.memory_recall_service = None

        # 根据配置初始化服务
        self._initialize_services()

        # 执行启动时同步
        self._perform_startup_sync()
    
    def _initialize_services(self):
        """初始化各种服务"""
        try:
            # 始终初始化旧记忆系统（保持兼容性）
            self.old_memory_service = MemoryService(
                root_dir=self.root_dir,
                api_key=self.api_key,
                base_url=self.base_url,
                model=self.model,
                max_token=self.max_token,
                temperature=self.temperature,
                max_groups=self.max_groups
            )
            
            # 根据配置决定是否初始化新记忆系统
            if config.memory_and_story.memory_system_type == "database":
                self.new_memory_service = DatabaseMemoryService(
                    root_dir=self.root_dir,
                    api_key=self.api_key,
                    base_url=self.base_url,
                    model=self.model,
                    max_token=self.max_token,
                    temperature=self.temperature,
                    max_groups=self.max_groups
                )
                logger.info("数据库记忆系统已初始化")
            
            # 根据配置决定是否初始化剧情系统
            if config.memory_and_story.enable_story_database:
                self.story_service = StoryService(
                    root_dir=self.root_dir,
                    api_key=self.api_key,
                    base_url=self.base_url,
                    model=self.model,
                    max_token=self.max_token,
                    temperature=self.temperature
                )
                logger.info("剧情数据库系统已初始化")
            
            # 初始化同步服务
            self.sync_initializer = SyncInitializer(self.root_dir)

            # 初始化记忆回忆服务
            self.memory_recall_service = MemoryRecallService()

            logger.info(f"记忆系统管理器初始化完成 - 使用: {config.memory_and_story.memory_system_type}")
            logger.info(f"记忆回忆服务状态: {self.memory_recall_service.get_service_status()}")
            
        except Exception as e:
            logger.error(f"初始化记忆系统服务失败: {e}")
    
    def _perform_startup_sync(self):
        """执行启动时的数据同步"""
        try:
            if config.memory_and_story.memory_system_type == "database" or config.memory_and_story.enable_story_database:
                logger.info("开始执行启动时数据同步...")
                success = self.sync_initializer.initialize_all_sync()
                if success:
                    logger.info("启动时数据同步完成")
                else:
                    logger.warning("启动时数据同步部分失败")
        except Exception as e:
            logger.error(f"启动时数据同步失败: {e}")
    
    def add_conversation(self, avatar_name: str, user_message: str, bot_reply: str, user_id: str, is_system_message: bool = False):
        """
        添加对话到记忆系统
        
        Args:
            avatar_name: 角色名称
            user_message: 用户消息
            bot_reply: 机器人回复
            user_id: 用户ID
            is_system_message: 是否为系统消息
        """
        try:
            # 根据配置选择记忆系统
            if config.memory_and_story.memory_system_type == "database" and self.new_memory_service:
                self.new_memory_service.add_conversation(avatar_name, user_message, bot_reply, user_id, is_system_message)
            else:
                self.old_memory_service.add_conversation(avatar_name, user_message, bot_reply, user_id, is_system_message)
            
            logger.debug(f"对话已添加到{config.memory_and_story.memory_system_type}记忆系统")
            
        except Exception as e:
            logger.error(f"添加对话到记忆系统失败: {e}")
    
    def get_recent_context(self, avatar_name: str, user_id: str, context_size: int = None) -> List[Dict]:
        """
        获取最近的对话上下文
        
        Args:
            avatar_name: 角色名称
            user_id: 用户ID
            context_size: 上下文大小
            
        Returns:
            List[Dict]: 对话上下文
        """
        try:
            # 根据配置选择记忆系统
            if config.memory_and_story.memory_system_type == "database" and self.new_memory_service:
                return self.new_memory_service.get_recent_context(avatar_name, user_id, context_size)
            else:
                return self.old_memory_service.get_recent_context(avatar_name, user_id, context_size)
                
        except Exception as e:
            logger.error(f"获取对话上下文失败: {e}")
            return []
    
    def get_core_memory(self, avatar_name: str, user_id: str) -> str:
        """
        获取核心记忆
        
        Args:
            avatar_name: 角色名称
            user_id: 用户ID
            
        Returns:
            str: 核心记忆内容
        """
        try:
            # 根据配置选择记忆系统
            if config.memory_and_story.memory_system_type == "database" and self.new_memory_service:
                return self.new_memory_service.get_core_memory(avatar_name, user_id)
            else:
                return self.old_memory_service.get_core_memory(avatar_name, user_id)
                
        except Exception as e:
            logger.error(f"获取核心记忆失败: {e}")
            return ""

    def get_comprehensive_memory(self, avatar_name: str, user_id: str, short_memory_limit: int = 10) -> Dict:
        """
        获取综合记忆数据（短期记忆 + 核心记忆）

        Args:
            avatar_name: 角色名称
            user_id: 用户ID
            short_memory_limit: 短期记忆查询数量限制

        Returns:
            Dict: 包含短期记忆和核心记忆的字典
        """
        try:
            # 根据配置选择记忆系统
            if config.memory_and_story.memory_system_type == "database" and self.new_memory_service:
                return self.new_memory_service.get_comprehensive_memory(avatar_name, user_id, short_memory_limit)
            else:
                # 旧记忆系统的兼容实现
                short_memory = self.old_memory_service.get_recent_context(avatar_name, user_id, short_memory_limit)
                core_memory = self.old_memory_service.get_core_memory(avatar_name, user_id)
                return {
                    'short_memory': short_memory,
                    'core_memory': core_memory
                }

        except Exception as e:
            logger.error(f"获取综合记忆数据失败: {e}")
            return {
                'short_memory': [],
                'core_memory': ""
            }

    def get_story_context(self, avatar_name: str, user_message: str) -> str:
        """
        获取相关剧情上下文

        Args:
            avatar_name: 角色名称
            user_message: 用户消息

        Returns:
            str: 格式化的剧情上下文
        """
        try:
            # 检查是否启用剧情系统
            if not config.memory_and_story.enable_story_database or not self.story_service:
                return ""

            # 检查是否启用剧情查询
            if not config.memory_and_story.story_query_enabled:
                return ""

            # 查询相关剧情
            relevant_story = self.story_service.query_relevant_story(
                avatar_name=avatar_name,
                user_message=user_message,
                max_results=config.memory_and_story.max_story_results
            )

            if relevant_story:
                # 格式化剧情上下文
                story_context = self.story_service.format_story_context(relevant_story)
                if story_context:
                    logger.debug(f"为角色 {avatar_name} 找到相关剧情上下文")
                    return f"\n\n=== 相关剧情背景 ===\n{story_context}\n=== 剧情背景结束 ==="

            return ""

        except Exception as e:
            logger.error(f"获取剧情上下文失败: {e}")
            return ""

    def get_recalled_memories(self, avatar_name: str, user_id: str, current_message: str) -> str:
        """
        获取智能回忆的记忆内容

        Args:
            avatar_name: 角色名称
            user_id: 用户ID
            current_message: 当前用户消息

        Returns:
            str: 格式化的回忆记忆内容，失败时返回空字符串
        """
        try:
            # 检查记忆回忆服务是否可用
            if not self.memory_recall_service or not self.memory_recall_service.is_enabled():
                logger.debug("记忆回忆服务未启用，跳过智能回忆")
                return ""

            # 获取历史对话记录
            history_conversations = []
            if config.memory_and_story.memory_system_type == "database" and self.new_memory_service:
                # 从数据库获取更多历史记录用于回忆
                memory_data = self.new_memory_service.get_comprehensive_memory(avatar_name, user_id, short_memory_limit=30)
                history_conversations = memory_data.get('short_memory', [])
            else:
                # 从旧系统获取历史记录
                recent_context = self.old_memory_service.get_recent_context(avatar_name, user_id, context_size=30)
                # 转换格式
                for i in range(0, len(recent_context), 2):
                    if i + 1 < len(recent_context):
                        user_msg = recent_context[i].get('content', '')
                        bot_msg = recent_context[i + 1].get('content', '')
                        if user_msg and bot_msg:
                            history_conversations.append({'user': user_msg, 'bot': bot_msg})

            # 获取核心记忆
            core_memory = self.get_core_memory(avatar_name, user_id)

            # 调用记忆回忆服务
            recalled_memory = self.memory_recall_service.recall_memories(
                current_message=current_message,
                history_conversations=history_conversations,
                core_memory=core_memory
            )

            if recalled_memory:
                logger.debug(f"成功回忆记忆，长度: {len(recalled_memory)}")
                return f"\n\n{recalled_memory}\n"
            else:
                logger.debug("未找到相关记忆")
                return ""

        except Exception as e:
            logger.error(f"智能记忆回忆失败: {e}")
            return ""
    
    def initialize_avatar_memory(self, avatar_name: str, user_id: str):
        """
        初始化角色记忆
        
        Args:
            avatar_name: 角色名称
            user_id: 用户ID
        """
        try:
            # 根据配置选择记忆系统
            if config.memory_and_story.memory_system_type == "database" and self.new_memory_service:
                self.new_memory_service.initialize_avatar_memory(avatar_name, user_id)
            else:
                self.old_memory_service.initialize_memory_files(avatar_name, user_id)
            
            # 如果启用剧情系统，创建默认剧情结构
            if config.memory_and_story.enable_story_database:
                self.sync_initializer.create_default_structure(avatar_name)
            
            logger.info(f"角色 {avatar_name} 记忆系统初始化完成")
            
        except Exception as e:
            logger.error(f"初始化角色记忆失败: {e}")
    
    def refresh_story_data(self, avatar_name: str) -> bool:
        """
        刷新角色剧情数据
        
        Args:
            avatar_name: 角色名称
            
        Returns:
            bool: 刷新是否成功
        """
        try:
            if not config.memory_and_story.enable_story_database or not self.story_service:
                logger.info("剧情系统未启用，跳过刷新")
                return True
            
            return self.story_service.refresh_story_data(avatar_name)
            
        except Exception as e:
            logger.error(f"刷新剧情数据失败: {e}")
            return False
    
    def get_memory_system_info(self) -> Dict[str, any]:
        """
        获取记忆系统信息

        Returns:
            Dict: 记忆系统状态信息
        """
        return {
            "memory_system_type": config.memory_and_story.memory_system_type,
            "story_database_enabled": config.memory_and_story.enable_story_database,
            "auto_memory_summary": config.memory_and_story.auto_memory_summary,
            "story_query_enabled": config.memory_and_story.story_query_enabled,
            "old_memory_service_available": self.old_memory_service is not None,
            "new_memory_service_available": self.new_memory_service is not None,
            "story_service_available": self.story_service is not None
        }

    # 兼容性方法 - 委托给底层记忆服务
    def update_core_memory(self, avatar_name: str, user_id: str, context: List[Dict]) -> bool:
        """
        更新核心记忆（兼容性方法）

        Args:
            avatar_name: 角色名称
            user_id: 用户ID
            context: 对话上下文

        Returns:
            bool: 更新是否成功
        """
        try:
            # 根据配置选择记忆系统
            if config.memory_and_story.memory_system_type == "database" and self.new_memory_service:
                return self.new_memory_service.update_core_memory(avatar_name, user_id, context)
            else:
                return self.old_memory_service.update_core_memory(avatar_name, user_id, context)

        except Exception as e:
            logger.error(f"更新核心记忆失败: {e}")
            return False

    def reset_short_memory(self, avatar_name: str, user_id: str) -> bool:
        """
        重置短期记忆

        Args:
            avatar_name: 角色名称
            user_id: 用户ID

        Returns:
            bool: 重置是否成功
        """
        try:
            # 根据配置选择记忆系统
            if config.memory_and_story.memory_system_type == "database" and self.new_memory_service:
                # 对于数据库记忆系统，清空短期记忆表
                import sqlite3
                db_path = self.new_memory_service._get_short_memory_db_path(avatar_name)
                logger.debug(f"短期记忆数据库路径: {db_path}")
                logger.debug(f"数据库文件是否存在: {os.path.exists(db_path)}")

                if os.path.exists(db_path):
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()

                    # 查询数据库中所有的user_id
                    cursor.execute('SELECT DISTINCT user_id FROM short_memory')
                    all_user_ids = cursor.fetchall()
                    logger.debug(f"数据库中所有的user_id: {[uid[0] for uid in all_user_ids]}")
                    logger.debug(f"要删除的user_id: '{user_id}'")

                    # 查询总记录数
                    cursor.execute('SELECT COUNT(*) FROM short_memory')
                    total_count = cursor.fetchone()[0]
                    logger.debug(f"数据库总记录数: {total_count}")

                    # 先查询删除前的记录数
                    cursor.execute('SELECT COUNT(*) FROM short_memory WHERE user_id = ?', (user_id,))
                    before_count = cursor.fetchone()[0]
                    logger.debug(f"删除前短期记忆记录数: {before_count}")

                    cursor.execute('DELETE FROM short_memory WHERE user_id = ?', (user_id,))
                    deleted_count = cursor.rowcount
                    conn.commit()

                    # 查询删除后的记录数
                    cursor.execute('SELECT COUNT(*) FROM short_memory WHERE user_id = ?', (user_id,))
                    after_count = cursor.fetchone()[0]
                    logger.debug(f"删除后短期记忆记录数: {after_count}")
                    logger.debug(f"实际删除的记录数: {deleted_count}")

                    conn.close()

                    # 同时清除MD文件
                    md_path = os.path.join(self.new_memory_service._get_avatar_newmemory_dir(avatar_name), "md", "short_memory.md")
                    if os.path.exists(md_path):
                        # 清空MD文件，写入空数组
                        import json
                        with open(md_path, 'w', encoding='utf-8') as f:
                            json.dump([], f, ensure_ascii=False, indent=2)
                        logger.info(f"已清空短期记忆MD文件: {md_path}")

                    logger.info(f"已重置数据库中 {avatar_name} 用户 {user_id} 的短期记忆，删除了 {deleted_count} 条记录")

                    # 清除LLM服务中的对话上下文，避免重新加载
                    try:
                        from src.services.ai.llm_service import LLMService
                        # 如果有全局的LLM服务实例，清除其上下文
                        # 这里需要根据实际的LLM服务实例来调用
                        logger.info(f"已清除用户 {user_id} 的内存对话上下文")
                    except Exception as e:
                        logger.debug(f"清除内存对话上下文时出错: {e}")

                else:
                    logger.warning(f"短期记忆数据库文件不存在: {db_path}")
                return True
            else:
                # 对于旧记忆系统，清空JSON文件
                short_memory_path = self.old_memory_service._get_short_memory_path(avatar_name, user_id)
                if os.path.exists(short_memory_path):
                    import json
                    with open(short_memory_path, "w", encoding="utf-8") as f:
                        json.dump([], f, ensure_ascii=False, indent=2)
                    logger.info(f"已重置文件中 {avatar_name} 用户 {user_id} 的短期记忆")
                return True

        except Exception as e:
            logger.error(f"重置短期记忆失败: {e}")
            return False

    def clear_core_memory(self, avatar_name: str, user_id: str) -> bool:
        """
        清空核心记忆

        Args:
            avatar_name: 角色名称
            user_id: 用户ID

        Returns:
            bool: 清空是否成功
        """
        try:
            # 根据配置选择记忆系统
            if config.memory_and_story.memory_system_type == "database" and self.new_memory_service:
                # 对于数据库记忆系统，清空核心记忆表
                import sqlite3
                db_path = self.new_memory_service._get_core_memory_db_path(avatar_name)
                logger.debug(f"核心记忆数据库路径: {db_path}")
                logger.debug(f"数据库文件是否存在: {os.path.exists(db_path)}")

                if os.path.exists(db_path):
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()

                    # 查询数据库中所有的user_id
                    cursor.execute('SELECT DISTINCT user_id FROM core_memory')
                    all_user_ids = cursor.fetchall()
                    logger.debug(f"核心记忆数据库中所有的user_id: {[uid[0] for uid in all_user_ids]}")
                    logger.debug(f"要删除的user_id: '{user_id}'")

                    # 查询总记录数
                    cursor.execute('SELECT COUNT(*) FROM core_memory')
                    total_count = cursor.fetchone()[0]
                    logger.debug(f"核心记忆数据库总记录数: {total_count}")

                    # 先查询删除前的记录数
                    cursor.execute('SELECT COUNT(*) FROM core_memory WHERE user_id = ?', (user_id,))
                    before_count = cursor.fetchone()[0]
                    logger.debug(f"删除前核心记忆记录数: {before_count}")

                    cursor.execute('DELETE FROM core_memory WHERE user_id = ?', (user_id,))
                    deleted_count = cursor.rowcount
                    conn.commit()

                    # 查询删除后的记录数
                    cursor.execute('SELECT COUNT(*) FROM core_memory WHERE user_id = ?', (user_id,))
                    after_count = cursor.fetchone()[0]
                    logger.debug(f"删除后核心记忆记录数: {after_count}")
                    logger.debug(f"实际删除的记录数: {deleted_count}")

                    conn.close()

                    # 同时清除MD文件
                    md_path = os.path.join(self.new_memory_service._get_avatar_newmemory_dir(avatar_name), "md", "core_memory.md")
                    if os.path.exists(md_path):
                        # 清空MD文件，写入默认内容
                        from datetime import datetime
                        default_content = f"""# {avatar_name} 的核心记忆

## 用户信息
- 用户ID: {user_id}
- 记录状态: 已清空

## 重要记忆
*暂无记忆内容*

---
*最后更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
*记录状态: 已清空*
"""
                        with open(md_path, 'w', encoding='utf-8') as f:
                            f.write(default_content)
                        logger.info(f"已清空核心记忆MD文件: {md_path}")

                    logger.info(f"已清空数据库中 {avatar_name} 用户 {user_id} 的核心记忆，删除了 {deleted_count} 条记录")
                else:
                    logger.warning(f"核心记忆数据库文件不存在: {db_path}")
                return True
            else:
                # 对于旧记忆系统，清空JSON文件
                from datetime import datetime
                import json
                core_memory_path = self.old_memory_service._get_core_memory_path(avatar_name, user_id)
                if os.path.exists(core_memory_path):
                    initial_core_data = {
                        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "content": ""
                    }
                    with open(core_memory_path, "w", encoding="utf-8") as f:
                        json.dump(initial_core_data, f, ensure_ascii=False, indent=2)
                    logger.info(f"已清空文件中 {avatar_name} 用户 {user_id} 的核心记忆")
                return True

        except Exception as e:
            logger.error(f"清空核心记忆失败: {e}")
            return False

    def has_user_memory(self, avatar_name: str, user_id: str) -> bool:
        """
        检查是否存在该用户的私聊记忆（兼容性方法）

        Args:
            avatar_name: 角色名称
            user_id: 用户ID

        Returns:
            bool: 如果存在私聊记忆返回True，否则返回False
        """
        try:
            # 根据配置选择记忆系统
            if config.memory_and_story.memory_system_type == "database" and self.new_memory_service:
                # 数据库记忆服务可能没有这个方法，使用旧服务
                return self.old_memory_service.has_user_memory(avatar_name, user_id)
            else:
                return self.old_memory_service.has_user_memory(avatar_name, user_id)

        except Exception as e:
            logger.error(f"检查用户记忆失败: {e}")
            return False

    def _get_short_memory_path(self, avatar_name: str, user_id: str) -> str:
        """
        获取短期记忆文件路径（兼容性方法）

        Args:
            avatar_name: 角色名称
            user_id: 用户ID

        Returns:
            str: 短期记忆文件路径
        """
        try:
            # 委托给旧记忆服务
            return self.old_memory_service._get_short_memory_path(avatar_name, user_id)
        except Exception as e:
            logger.error(f"获取短期记忆路径失败: {e}")
            return ""
